#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接浏览器自动化脚本
不依赖指纹浏览器API，直接使用Chrome远程调试
"""

import json
import time
import os
import subprocess
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

class DirectBrowserAutomation:
    def __init__(self):
        self.driver = None
        self.extracted_data = {}
        self.chrome_process = None
        
    def start_chrome_with_debug(self, debug_port=9222, user_data_dir=None):
        """启动Chrome浏览器并开启远程调试"""
        print(f"🚀 启动Chrome浏览器 (调试端口: {debug_port})")
        
        # Chrome启动参数
        chrome_args = [
            f"--remote-debugging-port={debug_port}",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-default-apps",
            "--disable-popup-blocking"
        ]
        
        # 如果指定了用户数据目录
        if user_data_dir:
            chrome_args.append(f"--user-data-dir={user_data_dir}")
        
        # 代理设置（如果需要）
        proxy_host = "127.0.0.1"
        proxy_port = "7890"
        chrome_args.append(f"--proxy-server={proxy_host}:{proxy_port}")
        
        try:
            # 查找Chrome可执行文件
            chrome_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",  # macOS
                "/Applications/RoxyBrowser.app/Contents/MacOS/RoxyBrowser",      # RoxyBrowser
                "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",    # Windows
                "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
                "/usr/bin/google-chrome",  # Linux
                "/usr/bin/chromium-browser"
            ]
            
            chrome_executable = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_executable = path
                    break
            
            if not chrome_executable:
                print("❌ 未找到Chrome可执行文件")
                return False
            
            print(f"✅ 找到Chrome: {chrome_executable}")
            
            # 启动Chrome进程
            cmd = [chrome_executable] + chrome_args
            self.chrome_process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待Chrome启动
            time.sleep(3)
            
            # 检查进程是否正常运行
            if self.chrome_process.poll() is None:
                print("✅ Chrome启动成功")
                return True
            else:
                print("❌ Chrome启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动Chrome失败: {e}")
            return False
    
    def connect_to_chrome(self, debug_port=9222):
        """连接到Chrome浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{debug_port}")
            
            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            print("✅ 成功连接到Chrome浏览器")
            return True
        except Exception as e:
            print(f"❌ 连接Chrome失败: {e}")
            return False
    
    def inject_extractor_script(self):
        """注入视频提取脚本"""
        if not self.driver:
            print("❌ 浏览器未连接")
            return False
        
        # 尝试读取提取脚本
        script_files = [
            'flextv_advanced_extractor.js',
            'flextv_episodes_extractor.js',
            'flextv_video_extractor.js'
        ]
        
        for script_file in script_files:
            if os.path.exists(script_file):
                try:
                    with open(script_file, 'r', encoding='utf-8') as f:
                        script_content = f.read()
                    
                    self.driver.execute_script(script_content)
                    print(f"✅ 成功注入脚本: {script_file}")
                    return True
                except Exception as e:
                    print(f"⚠️ 注入脚本失败 {script_file}: {e}")
        
        print("❌ 没有找到可用的提取脚本")
        return False
    
    def wait_for_extraction(self, timeout=300):
        """等待提取完成"""
        if not self.driver:
            return False
        
        print("⏳ 等待视频信息提取完成...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查不同的全局变量
                for var_name in ['flextv_advanced_data', 'flextv_data']:
                    result = self.driver.execute_script(f"return window.{var_name};")
                    if result:
                        self.extracted_data = result
                        print("✅ 视频信息提取完成！")
                        
                        # 显示统计信息
                        total_episodes = result.get('totalEpisodes', 0)
                        successful = result.get('successfulExtractions', 0)
                        print(f"📊 统计: {successful}/{total_episodes} 集提取成功")
                        
                        return True
                
                # 显示进度
                elapsed = int(time.time() - start_time)
                if elapsed % 10 == 0:  # 每10秒显示一次进度
                    print(f"⏳ 已等待 {elapsed} 秒...")
                
                time.sleep(3)
                
            except Exception as e:
                print(f"⚠️ 检查提取状态时出错: {e}")
                time.sleep(5)
        
        print("❌ 提取超时")
        return False
    
    def save_extracted_data(self):
        """保存提取的数据"""
        if not self.extracted_data:
            print("❌ 没有数据可保存")
            return False
        
        try:
            # 创建下载目录
            os.makedirs('downloads', exist_ok=True)
            
            # 保存完整数据
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            data_file = f"downloads/flextv_data_{timestamp}.json"
            
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 完整数据已保存: {data_file}")
            
            # 提取并保存视频链接
            video_links = []
            if 'videoData' in self.extracted_data:
                for episode in self.extracted_data['videoData']:
                    if episode.get('success') and episode.get('videoLinks'):
                        for link in episode['videoLinks']:
                            video_links.append({
                                'episode': episode['episodeNumber'],
                                'title': episode.get('episodeTitle', f"Episode {episode['episodeNumber']}"),
                                'url': link['url'],
                                'type': link['type'],
                                'quality': link.get('quality', 'unknown')
                            })
            elif 'allEpisodesData' in self.extracted_data:
                for episode in self.extracted_data['allEpisodesData']:
                    if episode.get('success') and episode.get('videoLinks'):
                        for link in episode['videoLinks']:
                            video_links.append({
                                'episode': episode['episodeNumber'],
                                'url': link['url'],
                                'type': link['type']
                            })
            
            if video_links:
                links_file = f"downloads/video_links_{timestamp}.json"
                with open(links_file, 'w', encoding='utf-8') as f:
                    json.dump(video_links, f, ensure_ascii=False, indent=2)
                
                print(f"🔗 视频链接已保存: {links_file}")
                print(f"🎥 总共找到 {len(video_links)} 个视频链接")
                
                # 显示前几个链接作为示例
                print("\n📺 视频链接示例:")
                for i, link in enumerate(video_links[:5]):
                    print(f"  第{link['episode']}集: {link['url']}")
                
                if len(video_links) > 5:
                    print(f"  ... 还有 {len(video_links) - 5} 个链接")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
                print("✅ 浏览器连接已关闭")
            except:
                pass
            self.driver = None
        
        if self.chrome_process:
            try:
                self.chrome_process.terminate()
                self.chrome_process.wait(timeout=5)
                print("✅ Chrome进程已终止")
            except:
                try:
                    self.chrome_process.kill()
                    print("✅ Chrome进程已强制终止")
                except:
                    pass
            self.chrome_process = None
    
    def run_extraction(self, video_url):
        """运行完整的提取流程"""
        print("🎬 直接浏览器自动化 - FlexTV视频提取")
        print("=" * 60)
        print(f"🎯 目标视频: {video_url}")
        
        try:
            # 1. 启动Chrome浏览器
            if not self.start_chrome_with_debug():
                return False
            
            # 2. 连接到Chrome
            if not self.connect_to_chrome():
                return False
            
            # 3. 访问视频页面
            print(f"🌐 访问视频页面...")
            self.driver.get(video_url)
            time.sleep(5)
            
            # 4. 注入提取脚本
            if not self.inject_extractor_script():
                return False
            
            # 5. 等待提取完成
            if not self.wait_for_extraction():
                return False
            
            # 6. 保存数据
            if not self.save_extracted_data():
                return False
            
            print("🎉 视频信息提取完成！")
            return True
            
        except Exception as e:
            print(f"❌ 提取过程中出错: {e}")
            return False
        
        finally:
            # 清理资源
            self.cleanup()

def main():
    """主函数"""
    video_url = "https://www.flextv.cc/video/The_Secret_Recipe_to_Snatch_a_Billionaire-J9zD5pbn1x"
    
    automation = DirectBrowserAutomation()
    success = automation.run_extraction(video_url)
    
    if success:
        print("\n🎉 任务完成！")
        print("📁 请查看 downloads 目录中的提取结果")
    else:
        print("\n❌ 任务失败！")
        print("💡 请检查网络连接和代理设置")

if __name__ == "__main__":
    main()
