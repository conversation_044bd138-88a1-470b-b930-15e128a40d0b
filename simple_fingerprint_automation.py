#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版指纹浏览器自动化脚本
直接使用现有的浏览器配置文件
"""

import requests
import json
import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

class SimpleFingerprintAutomation:
    def __init__(self, api_host="127.0.0.1", api_port="50000"):
        self.api_host = api_host
        self.api_port = api_port
        self.base_url = f"http://{api_host}:{api_port}"
        self.driver = None
        self.extracted_data = {}
        
    def list_profiles(self):
        """列出所有浏览器配置文件"""
        url = f"{self.base_url}/api/v1/profile/list"
        
        try:
            response = requests.get(url)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    profiles = result.get("data", {}).get("list", [])
                    print(f"✅ 找到 {len(profiles)} 个浏览器配置文件:")
                    for i, profile in enumerate(profiles):
                        print(f"  {i+1}. {profile['name']} (ID: {profile['id']})")
                    return profiles
                else:
                    print(f"❌ 获取配置文件列表失败: {result.get('msg')}")
            else:
                print(f"❌ API请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 获取配置文件列表异常: {e}")
        
        return []
    
    def start_browser_by_id(self, profile_id):
        """通过ID启动指纹浏览器"""
        url = f"{self.base_url}/api/v1/profile/start"
        
        data = {
            "id": profile_id
        }
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    debug_port = result["data"]["ws"]["selenium"]
                    print(f"✅ 浏览器启动成功，调试端口: {debug_port}")
                    
                    # 连接到浏览器
                    return self.connect_to_browser(debug_port)
                else:
                    print(f"❌ 启动浏览器失败: {result.get('msg')}")
            else:
                print(f"❌ API请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 启动浏览器异常: {e}")
        
        return False
    
    def connect_to_browser(self, debug_port):
        """连接到浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{debug_port}")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ 成功连接到指纹浏览器")
            return True
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            return False
    
    def stop_browser_by_id(self, profile_id):
        """通过ID停止浏览器"""
        if self.driver:
            self.driver.quit()
            self.driver = None
        
        url = f"{self.base_url}/api/v1/profile/stop"
        
        data = {
            "id": profile_id
        }
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    print("✅ 浏览器已停止")
                    return True
                else:
                    print(f"⚠️ 停止浏览器失败: {result.get('msg')}")
            else:
                print(f"⚠️ API请求失败: {response.status_code}")
        except Exception as e:
            print(f"⚠️ 停止浏览器异常: {e}")
        
        return False
    
    def inject_extractor_script(self):
        """注入视频提取脚本"""
        if not self.driver:
            print("❌ 浏览器未连接")
            return False
        
        # 尝试读取提取脚本
        script_files = [
            'flextv_advanced_extractor.js',
            'flextv_episodes_extractor.js',
            'flextv_video_extractor.js'
        ]
        
        for script_file in script_files:
            if os.path.exists(script_file):
                try:
                    with open(script_file, 'r', encoding='utf-8') as f:
                        script_content = f.read()
                    
                    self.driver.execute_script(script_content)
                    print(f"✅ 成功注入脚本: {script_file}")
                    return True
                except Exception as e:
                    print(f"⚠️ 注入脚本失败 {script_file}: {e}")
        
        print("❌ 没有找到可用的提取脚本")
        return False
    
    def wait_for_extraction(self, timeout=300):
        """等待提取完成"""
        if not self.driver:
            return False
        
        print("⏳ 等待视频信息提取完成...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查不同的全局变量
                for var_name in ['flextv_advanced_data', 'flextv_data']:
                    result = self.driver.execute_script(f"return window.{var_name};")
                    if result:
                        self.extracted_data = result
                        print("✅ 视频信息提取完成！")
                        
                        # 显示统计信息
                        total_episodes = result.get('totalEpisodes', 0)
                        successful = result.get('successfulExtractions', 0)
                        print(f"📊 统计: {successful}/{total_episodes} 集提取成功")
                        
                        return True
                
                time.sleep(3)
                
            except Exception as e:
                print(f"⚠️ 检查提取状态时出错: {e}")
                time.sleep(5)
        
        print("❌ 提取超时")
        return False
    
    def save_extracted_data(self):
        """保存提取的数据"""
        if not self.extracted_data:
            print("❌ 没有数据可保存")
            return False
        
        try:
            # 创建下载目录
            os.makedirs('downloads', exist_ok=True)
            
            # 保存完整数据
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            data_file = f"downloads/flextv_data_{timestamp}.json"
            
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 完整数据已保存: {data_file}")
            
            # 提取并保存视频链接
            video_links = []
            if 'videoData' in self.extracted_data:
                for episode in self.extracted_data['videoData']:
                    if episode.get('success') and episode.get('videoLinks'):
                        for link in episode['videoLinks']:
                            video_links.append({
                                'episode': episode['episodeNumber'],
                                'title': episode.get('episodeTitle', f"Episode {episode['episodeNumber']}"),
                                'url': link['url'],
                                'type': link['type'],
                                'quality': link.get('quality', 'unknown')
                            })
            elif 'allEpisodesData' in self.extracted_data:
                for episode in self.extracted_data['allEpisodesData']:
                    if episode.get('success') and episode.get('videoLinks'):
                        for link in episode['videoLinks']:
                            video_links.append({
                                'episode': episode['episodeNumber'],
                                'url': link['url'],
                                'type': link['type']
                            })
            
            if video_links:
                links_file = f"downloads/video_links_{timestamp}.json"
                with open(links_file, 'w', encoding='utf-8') as f:
                    json.dump(video_links, f, ensure_ascii=False, indent=2)
                
                print(f"🔗 视频链接已保存: {links_file}")
                print(f"🎥 总共找到 {len(video_links)} 个视频链接")
                
                # 显示前几个链接作为示例
                print("\n📺 视频链接示例:")
                for i, link in enumerate(video_links[:5]):
                    print(f"  第{link['episode']}集: {link['url']}")
                
                if len(video_links) > 5:
                    print(f"  ... 还有 {len(video_links) - 5} 个链接")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
            return False
    
    def run_interactive(self):
        """交互式运行"""
        print("🎬 简化版指纹浏览器自动化")
        print("=" * 50)
        
        # 1. 列出配置文件
        profiles = self.list_profiles()
        if not profiles:
            print("❌ 没有找到浏览器配置文件")
            return False
        
        # 2. 选择配置文件
        try:
            choice = input(f"\n请选择配置文件 (1-{len(profiles)}): ")
            profile_index = int(choice) - 1
            if profile_index < 0 or profile_index >= len(profiles):
                print("❌ 无效的选择")
                return False
            
            selected_profile = profiles[profile_index]
            profile_id = selected_profile['id']
            print(f"✅ 选择了配置文件: {selected_profile['name']}")
            
        except (ValueError, KeyboardInterrupt):
            print("❌ 操作取消")
            return False
        
        # 3. 启动浏览器
        if not self.start_browser_by_id(profile_id):
            return False
        
        try:
            # 4. 访问视频页面
            video_url = "https://www.flextv.cc/video/The_Secret_Recipe_to_Snatch_a_Billionaire-J9zD5pbn1x"
            print(f"🌐 访问视频页面: {video_url}")
            self.driver.get(video_url)
            time.sleep(5)
            
            # 5. 注入提取脚本
            if not self.inject_extractor_script():
                return False
            
            # 6. 等待提取完成
            if not self.wait_for_extraction():
                return False
            
            # 7. 保存数据
            if not self.save_extracted_data():
                return False
            
            print("🎉 视频信息提取完成！")
            return True
            
        except Exception as e:
            print(f"❌ 提取过程中出错: {e}")
            return False
        
        finally:
            # 清理资源
            self.stop_browser_by_id(profile_id)

def main():
    """主函数"""
    automation = SimpleFingerprintAutomation()
    success = automation.run_interactive()
    
    if success:
        print("\n🎉 任务完成！")
        print("📁 请查看 downloads 目录中的提取结果")
    else:
        print("\n❌ 任务失败！")

if __name__ == "__main__":
    main()
