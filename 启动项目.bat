@echo off
chcp 65001 >nul
title 浏览器指纹学术研究项目启动器

echo.
echo ========================================
echo 🎓 浏览器指纹学术研究项目启动器
echo Browser Fingerprint Academic Research
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.6或更高版本
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测成功
echo.

:: 检查项目文件
echo 🔍 检查项目文件...
set missing_files=0

if not exist "browser_fingerprint_collector.js" (
    echo ❌ 缺少文件: browser_fingerprint_collector.js
    set missing_files=1
)

if not exist "fingerprint_analyzer.py" (
    echo ❌ 缺少文件: fingerprint_analyzer.py
    set missing_files=1
)

if not exist "启动项目.html" (
    echo ❌ 缺少文件: 启动项目.html
    set missing_files=1
)

if not exist "学术研究工具使用指南.md" (
    echo ❌ 缺少文件: 学术研究工具使用指南.md
    set missing_files=1
)

if %missing_files%==1 (
    echo.
    echo ❌ 项目文件不完整，请确保所有文件都在当前目录中
    pause
    exit /b 1
)

echo ✅ 项目文件检查完成
echo.

:: 启动Python服务器
echo 🚀 启动项目服务器...
echo.
echo 💡 提示: 
echo    - 服务器启动后会自动打开浏览器
echo    - 按 Ctrl+C 可以停止服务器
echo    - 请保持此窗口打开
echo.

python start_project.py

echo.
echo 👋 感谢使用浏览器指纹学术研究工具！
pause
