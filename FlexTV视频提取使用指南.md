# FlexTV 视频下载链接提取指南

## 🎯 项目概述

本项目提供了一套完整的工具来提取 FlexTV 网站上的视频下载链接和剧集信息，专门针对需要使用指纹浏览器才能访问的情况。

## 📁 文件说明

### JavaScript 提取脚本

1. **`flextv_video_extractor.js`** - 基础视频信息提取器
2. **`flextv_episodes_extractor.js`** - 全集视频提取器（iframe 方式）
3. **`flextv_advanced_extractor.js`** - 高级全集提取器（API 优化版，推荐）

### Python 处理脚本

3. **`flextv_data_processor.py`** - 数据处理和分析工具

### 示例数据

4. **`flextv_dramas_complete.json`** - 示例短剧列表数据
5. **`flextv_dramas.csv`** - CSV 格式的短剧数据

## 📊 脚本对比

| 脚本                           | 功能            | 优点                     | 缺点                       | 推荐场景     |
| ------------------------------ | --------------- | ------------------------ | -------------------------- | ------------ |
| `flextv_video_extractor.js`    | 基础信息提取    | 快速、简单               | 只能获取当前页面信息       | 单集测试     |
| `flextv_episodes_extractor.js` | iframe 全集提取 | 能获取所有剧集视频链接   | 可能遇到跨域限制，速度较慢 | 小规模剧集   |
| `flextv_advanced_extractor.js` | API 优化提取    | 高效、成功率高、并发处理 | 依赖 API 结构              | **推荐使用** |

## 🚀 使用步骤

### 方法一：高级 API 提取器（推荐）

1. **打开指纹浏览器**，访问 FlexTV 视频页面

   ```
   例如: https://www.flextv.cc/video/The_Secret_Recipe_to_Snatch_a_Billionaire-J9zD5pbn1x
   ```

2. **打开开发者工具**

   - 按 `F12` 或右键选择"检查"
   - 切换到 `Console` 标签

3. **运行高级提取脚本**

   - 复制 `flextv_advanced_extractor.js` 的全部内容
   - 粘贴到控制台并按回车执行

4. **等待提取完成**
   - 脚本会自动通过 API 获取所有剧集信息
   - 并发处理，速度更快，成功率更高
   - 自动下载两个 JSON 文件：
     - 完整数据文件（包含所有信息）
     - 纯视频链接文件（仅包含视频下载地址）

### 方法二：iframe 全集提取器（备用）

1. **运行 iframe 提取脚本**

   - 复制 `flextv_episodes_extractor.js` 的全部内容
   - 粘贴到控制台并按回车执行

2. **等待逐集提取**
   - 脚本会自动访问每一集页面
   - 提取过程较慢，但兼容性更好
   - 显示实时进度和统计信息
   - 自动下载完整数据和视频链接两个文件

### 方法三：基础提取器（单集测试）

1. **运行基础提取脚本**

   - 复制 `flextv_video_extractor.js` 的全部内容
   - 粘贴到控制台并按回车执行

2. **快速获取当前页面信息**
   - 仅提取当前页面的基础信息
   - 适合测试和调试使用

### 第二步：处理提取的数据

1. **运行 Python 处理脚本**

   ```bash
   python flextv_data_processor.py
   ```

2. **查看分析结果**
   - 脚本会自动找到并处理所有 FlexTV JSON 文件
   - 生成详细的分析报告和整理后的数据

## 📊 提取的数据内容

### 剧集信息

- ✅ 剧集编号和标题
- ✅ 每集的播放链接
- ✅ 缩略图地址
- ✅ 当前播放状态

### 视频源

- ✅ 直接视频链接
- ✅ M3U8 流媒体链接
- ✅ 多种格式的视频源

### API 数据

- ✅ 网络请求记录
- ✅ API 响应数据
- ✅ 加密数据片段

## 🔧 高级功能

### 网络请求拦截

脚本会自动拦截以下类型的请求：

- `api-quick.flextv.cc` 的 API 调用
- 包含 `video`、`m3u8`、`mp4` 的 URL
- 其他媒体相关的网络请求

### 数据解密

对于加密的 API 响应，脚本会：

- 识别加密数据格式
- 记录加密数据长度和特征
- 为后续解密提供基础信息

### 多格式输出

处理后的数据会保存为：

- **JSON 格式** - 完整的结构化数据
- **CSV 格式** - 便于 Excel 处理的表格数据
- **分析报告** - 包含统计信息的详细报告

## 📝 使用示例

### 控制台输出示例

```
🎭 FlexTV 多集视频提取器启动...
🆔 Series ID: J9zD5pbn1x
🌐 [FETCH] https://api-quick.flextv.cc/webGetSeriesSectionFullList?series_id=J9zD5pbn1x&series_no=1
📺 找到 30 集
🎥 找到 2 个视频源
✅ 提取完成！
📁 数据已自动下载
💾 数据已保存到 window.flextv_data
```

### 数据结构示例

```json
{
  "seriesId": "J9zD5pbn1x",
  "title": "The Secret Recipe to Snatch a Billionaire",
  "totalEpisodes": 30,
  "episodes": [
    {
      "number": 1,
      "title": "Episode 1",
      "link": "https://www.flextv.cc/video/...",
      "thumbnail": "https://file-cdn.flextv.cc/...",
      "isActive": true
    }
  ],
  "videoSources": [
    {
      "src": "https://video-cdn.flextv.cc/video.m3u8",
      "type": "application/x-mpegURL"
    }
  ]
}
```

## ⚠️ 注意事项

### 地区限制

- FlexTV 有严格的地区限制
- 必须使用指纹浏览器或 VPN
- 某些视频可能显示"不在您的地区可用"

### API 认证

- 部分 API 需要登录认证
- 未登录状态下可能返回"需要先登录"错误
- 建议在登录状态下运行脚本

### 数据加密

- FlexTV 使用了数据加密机制
- 某些 API 响应是加密的 Base64 字符串
- 需要进一步分析加密算法

## 🛠️ 故障排除

### 常见问题

1. **脚本无法运行**

   - 确保在正确的页面运行脚本
   - 检查浏览器控制台是否有错误信息
   - 尝试刷新页面后重新运行

2. **提取不到剧集信息**

   - 确认页面已完全加载
   - 检查是否有地区限制提示
   - 尝试登录后再运行

3. **API 请求失败**
   - 检查网络连接
   - 确认是否需要登录
   - 查看控制台的错误信息

### 调试技巧

1. **查看网络请求**

   - 打开开发者工具的 Network 标签
   - 筛选 XHR/Fetch 请求
   - 查找包含视频数据的 API 调用

2. **检查页面元素**
   - 使用 Elements 标签检查页面结构
   - 查找剧集列表的 HTML 结构
   - 确认视频播放器的实现方式

## 📈 扩展功能

### 批量处理

可以修改脚本来批量处理多个视频：

```javascript
// 批量提取多个series_id的数据
const seriesIds = ["J9zD5pbn1x", "rbz5Vrqnde", "..."];
for (const id of seriesIds) {
  // 处理每个series
}
```

### 自动下载

结合下载工具实现自动下载：

```python
# 使用提取的链接自动下载视频
import requests
for link in video_links:
    # 下载视频文件
    download_video(link['url'])
```

## 📞 技术支持

如果遇到问题，请检查：

1. 浏览器控制台的错误信息
2. 网络请求的响应状态
3. 页面是否正确加载
4. 是否有地区限制或登录要求

---

**免责声明**: 本工具仅用于学习和研究目的，请遵守相关网站的使用条款和版权规定。
