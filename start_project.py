#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器指纹学术研究项目启动器
Browser Fingerprint Academic Research Project Launcher
"""

import os
import sys
import webbrowser
import http.server
import socketserver
import threading
import time
from pathlib import Path

class ResearchProjectLauncher:
    def __init__(self):
        self.project_dir = Path(__file__).parent
        self.port = 8080
        self.server = None
        self.server_thread = None
        
    def check_files(self):
        """检查项目文件是否存在"""
        required_files = [
            'browser_fingerprint_collector.js',
            'fingerprint_analyzer.py',
            '启动项目.html',
            '学术研究工具使用指南.md'
        ]
        
        missing_files = []
        for file in required_files:
            if not (self.project_dir / file).exists():
                missing_files.append(file)
        
        if missing_files:
            print("❌ 缺少以下文件:")
            for file in missing_files:
                print(f"   - {file}")
            return False
        
        print("✅ 所有项目文件检查完成")
        return True
    
    def find_available_port(self):
        """查找可用端口"""
        import socket
        
        for port in range(8080, 8090):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    self.port = port
                    return True
            except OSError:
                continue
        
        print("❌ 无法找到可用端口")
        return False
    
    def start_server(self):
        """启动HTTP服务器"""
        try:
            os.chdir(self.project_dir)
            
            class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
                def end_headers(self):
                    # 添加CORS头部以支持跨域请求
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    super().end_headers()
                
                def log_message(self, format, *args):
                    # 自定义日志格式
                    print(f"🌐 [{time.strftime('%H:%M:%S')}] {format % args}")
            
            self.server = socketserver.TCPServer(("localhost", self.port), CustomHTTPRequestHandler)
            
            def run_server():
                print(f"🚀 HTTP服务器启动在 http://localhost:{self.port}")
                self.server.serve_forever()
            
            self.server_thread = threading.Thread(target=run_server, daemon=True)
            self.server_thread.start()
            
            return True
            
        except Exception as e:
            print(f"❌ 服务器启动失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        url = f"http://localhost:{self.port}/启动项目.html"
        
        try:
            print(f"🌐 正在打开浏览器: {url}")
            webbrowser.open(url)
            return True
        except Exception as e:
            print(f"❌ 无法打开浏览器: {e}")
            print(f"请手动访问: {url}")
            return False
    
    def show_project_info(self):
        """显示项目信息"""
        print("=" * 60)
        print("🎓 浏览器指纹学术研究项目")
        print("Browser Fingerprint Academic Research Project")
        print("=" * 60)
        print()
        print("📁 项目目录:", self.project_dir)
        print("🌐 服务器地址:", f"http://localhost:{self.port}")
        print()
        print("📋 项目文件:")
        
        files_info = {
            'browser_fingerprint_collector.js': '🔧 核心指纹收集工具',
            'fingerprint_analyzer.py': '📊 Python数据分析工具',
            '启动项目.html': '🚀 项目启动页面',
            '学术研究工具使用指南.md': '📚 详细使用文档'
        }
        
        for file, desc in files_info.items():
            if (self.project_dir / file).exists():
                size = (self.project_dir / file).stat().st_size
                print(f"   ✅ {file} ({size:,} bytes) - {desc}")
            else:
                print(f"   ❌ {file} - 文件缺失")
        
        print()
        print("⚠️ 重要提醒:")
        print("   - 本工具仅用于学术研究和教育目的")
        print("   - 请遵守相关法律法规和道德准则")
        print("   - 不得用于恶意跟踪或隐私侵犯")
        print()
    
    def show_usage_instructions(self):
        """显示使用说明"""
        print("🛠️ 使用说明:")
        print("=" * 40)
        print("1. 📖 阅读使用指南")
        print("   - 在浏览器中查看项目文档")
        print("   - 了解工具功能和研究应用")
        print()
        print("2. 🧪 运行测试")
        print("   - 点击'加载测试页面'创建测试环境")
        print("   - 在测试页面中运行研究工具")
        print()
        print("3. 🔬 开始研究")
        print("   - 在目标网站打开开发者工具")
        print("   - 复制粘贴研究工具代码")
        print("   - 观察控制台输出和数据收集")
        print()
        print("4. 📊 分析数据")
        print("   - 下载生成的JSON报告")
        print("   - 使用Python分析工具处理数据")
        print("   - 生成研究报告和可视化图表")
        print()
        print("🔧 快捷操作:")
        print("   - Ctrl+C: 停止服务器")
        print("   - 浏览器F12: 打开开发者工具")
        print("   - 控制台运行: 执行研究工具代码")
        print()
    
    def wait_for_exit(self):
        """等待用户退出"""
        try:
            print("💡 提示: 按 Ctrl+C 停止服务器并退出")
            print("🌐 服务器运行中，请保持此窗口打开...")
            print()
            
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            if self.server:
                self.server.shutdown()
                self.server.server_close()
            print("✅ 服务器已停止")
            print("👋 感谢使用浏览器指纹学术研究工具！")
    
    def run(self):
        """运行项目启动器"""
        print("🎓 启动浏览器指纹学术研究项目...")
        print()
        
        # 检查文件
        if not self.check_files():
            print("❌ 项目文件检查失败，请确保所有文件都存在")
            return False
        
        # 查找可用端口
        if not self.find_available_port():
            return False
        
        # 显示项目信息
        self.show_project_info()
        
        # 启动服务器
        if not self.start_server():
            return False
        
        # 等待服务器启动
        time.sleep(1)
        
        # 打开浏览器
        self.open_browser()
        
        # 显示使用说明
        self.show_usage_instructions()
        
        # 等待用户退出
        self.wait_for_exit()
        
        return True

def main():
    """主函数"""
    print("🎓 浏览器指纹学术研究项目启动器")
    print("Browser Fingerprint Academic Research Project Launcher")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        return
    
    # 创建并运行启动器
    launcher = ResearchProjectLauncher()
    
    try:
        success = launcher.run()
        if not success:
            print("❌ 项目启动失败")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 启动过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
