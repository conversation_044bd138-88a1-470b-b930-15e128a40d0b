// FlexTV 视频下载链接提取器
// 在视频页面的浏览器控制台中运行此脚本

(function() {
    'use strict';
    
    console.log('🎬 FlexTV 视频提取器启动...');
    
    // 提取视频信息的主函数
    function extractVideoInfo() {
        const videoInfo = {
            title: '',
            seriesId: '',
            episodes: [],
            videoSources: [],
            apiData: {}
        };
        
        // 1. 提取页面标题
        videoInfo.title = document.title.replace(' - FlexTV', '');
        console.log('📺 视频标题:', videoInfo.title);
        
        // 2. 从URL提取series_id
        const urlMatch = window.location.pathname.match(/([^-]+)$/);
        if (urlMatch) {
            videoInfo.seriesId = urlMatch[1];
            console.log('🆔 Series ID:', videoInfo.seriesId);
        }
        
        // 3. 查找视频元素
        const videos = document.querySelectorAll('video');
        videos.forEach((video, index) => {
            const videoData = {
                index: index,
                src: video.src || video.currentSrc,
                sources: []
            };
            
            // 获取source标签
            const sources = video.querySelectorAll('source');
            sources.forEach(source => {
                videoData.sources.push({
                    src: source.src,
                    type: source.type
                });
            });
            
            if (videoData.src || videoData.sources.length > 0) {
                videoInfo.videoSources.push(videoData);
            }
        });
        
        // 4. 查找剧集列表
        const episodeElements = document.querySelectorAll('[class*="episode"], [class*="section"], .drama_list .item');
        episodeElements.forEach((element, index) => {
            const episodeData = {
                index: index + 1,
                title: '',
                link: '',
                thumbnail: ''
            };
            
            const titleEl = element.querySelector('.title, h3, .episode-title');
            if (titleEl) {
                episodeData.title = titleEl.textContent.trim();
            }
            
            const linkEl = element.querySelector('a');
            if (linkEl) {
                episodeData.link = linkEl.href;
            }
            
            const imgEl = element.querySelector('img');
            if (imgEl) {
                episodeData.thumbnail = imgEl.src;
            }
            
            if (episodeData.title || episodeData.link) {
                videoInfo.episodes.push(episodeData);
            }
        });
        
        return videoInfo;
    }
    
    // 监听网络请求的函数
    function interceptNetworkRequests() {
        const originalFetch = window.fetch;
        const originalXHR = window.XMLHttpRequest.prototype.open;
        
        const apiCalls = [];
        
        // 拦截fetch请求
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && (
                url.includes('api-quick.flextv.cc') || 
                url.includes('video') || 
                url.includes('m3u8') || 
                url.includes('mp4')
            )) {
                console.log('🌐 拦截到API请求:', url);
                apiCalls.push({
                    type: 'fetch',
                    url: url,
                    timestamp: Date.now()
                });
            }
            return originalFetch.apply(this, args);
        };
        
        // 拦截XMLHttpRequest
        window.XMLHttpRequest.prototype.open = function(method, url, ...args) {
            if (typeof url === 'string' && (
                url.includes('api-quick.flextv.cc') || 
                url.includes('video') || 
                url.includes('m3u8') || 
                url.includes('mp4')
            )) {
                console.log('🌐 拦截到XHR请求:', url);
                apiCalls.push({
                    type: 'xhr',
                    method: method,
                    url: url,
                    timestamp: Date.now()
                });
            }
            return originalXHR.apply(this, [method, url, ...args]);
        };
        
        return apiCalls;
    }
    
    // 尝试获取API数据
    async function fetchVideoData(seriesId) {
        const apiData = {};
        
        try {
            // 尝试获取剧集列表
            const seriesUrl = `https://api-quick.flextv.cc/webGetSeriesSectionFullList?series_id=${seriesId}&series_no=1`;
            console.log('🔍 尝试获取剧集数据:', seriesUrl);
            
            const response = await fetch(seriesUrl);
            const data = await response.json();
            apiData.seriesData = data;
            console.log('📊 剧集数据:', data);
            
        } catch (error) {
            console.log('❌ API请求失败:', error.message);
            apiData.error = error.message;
        }
        
        try {
            // 尝试获取语言信息
            const langUrl = `https://api-quick.flextv.cc/webGetSeriesLang?series_id=${seriesId}`;
            const langResponse = await fetch(langUrl);
            const langData = await langResponse.json();
            apiData.langData = langData;
            console.log('🌍 语言数据:', langData);
            
        } catch (error) {
            console.log('❌ 语言API请求失败:', error.message);
        }
        
        return apiData;
    }
    
    // 查找页面中的加密数据
    function findEncryptedData() {
        const scripts = document.querySelectorAll('script');
        const encryptedData = [];
        
        scripts.forEach(script => {
            const content = script.textContent || script.innerHTML;
            
            // 查找可能的加密视频数据
            if (content.includes('video') || content.includes('m3u8') || content.includes('mp4')) {
                encryptedData.push({
                    type: 'video_script',
                    content: content.substring(0, 1000) // 只取前1000字符
                });
            }
            
            // 查找API响应数据
            if (content.includes('webGetSeriesSectionFullList') || content.includes('series_id')) {
                encryptedData.push({
                    type: 'api_data',
                    content: content.substring(0, 1000)
                });
            }
        });
        
        return encryptedData;
    }
    
    // 主执行函数
    async function main() {
        console.log('🚀 开始提取视频信息...');
        
        // 1. 拦截网络请求
        const apiCalls = interceptNetworkRequests();
        
        // 2. 提取基本视频信息
        const videoInfo = extractVideoInfo();
        
        // 3. 查找加密数据
        const encryptedData = findEncryptedData();
        
        // 4. 尝试获取API数据
        let apiData = {};
        if (videoInfo.seriesId) {
            apiData = await fetchVideoData(videoInfo.seriesId);
        }
        
        // 5. 整合所有数据
        const result = {
            ...videoInfo,
            apiData,
            encryptedData,
            networkRequests: apiCalls,
            extractTime: new Date().toISOString(),
            pageUrl: window.location.href
        };
        
        // 6. 输出结果
        console.log('✅ 提取完成！');
        console.log('📋 完整结果:', result);
        
        // 7. 创建下载链接
        const dataStr = JSON.stringify(result, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = `flextv_${videoInfo.seriesId || 'video'}_data.json`;
        downloadLink.textContent = '📥 下载视频数据';
        downloadLink.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            background: #007bff;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(downloadLink);
        
        // 8. 自动点击下载
        setTimeout(() => {
            downloadLink.click();
        }, 1000);
        
        return result;
    }
    
    // 执行主函数
    main().catch(console.error);
    
})();

// 使用说明
console.log(`
🎬 FlexTV 视频提取器使用说明：

1. 在指纹浏览器中打开 FlexTV 视频页面
2. 打开浏览器开发者工具 (F12)
3. 切换到 Console 标签
4. 复制粘贴此脚本并按回车执行
5. 脚本会自动提取视频信息并下载JSON文件
6. 查看控制台输出获取详细信息

提取的信息包括：
- 视频标题和ID
- 剧集列表
- 视频源链接
- API请求数据
- 加密数据片段
`);
