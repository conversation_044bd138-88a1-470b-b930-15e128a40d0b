#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器指纹分析器 - 仅用于学习和研究目的
Browser Fingerprint Analyzer - For Educational Purposes Only

警告：此代码仅用于学习浏览器指纹技术原理，不应用于恶意目的
"""

import json
import hashlib
import os
from datetime import datetime
from collections import Counter
import matplotlib.pyplot as plt
import pandas as pd

class FingerprintAnalyzer:
    def __init__(self):
        self.fingerprints = []
        self.analysis_results = {}
    
    def load_fingerprint_file(self, file_path):
        """加载指纹数据文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.fingerprints.append(data)
                print(f"✅ 成功加载指纹文件: {file_path}")
                return True
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return False
    
    def load_all_fingerprints(self, directory='.'):
        """加载目录中的所有指纹文件"""
        fingerprint_files = [f for f in os.listdir(directory) 
                           if f.startswith('browser_fingerprint_') and f.endswith('.json')]
        
        if not fingerprint_files:
            print("❌ 未找到指纹文件")
            return False
        
        print(f"📁 找到 {len(fingerprint_files)} 个指纹文件")
        
        for file in fingerprint_files:
            self.load_fingerprint_file(os.path.join(directory, file))
        
        return len(self.fingerprints) > 0
    
    def analyze_uniqueness(self):
        """分析指纹的唯一性"""
        if not self.fingerprints:
            print("❌ 没有指纹数据可分析")
            return
        
        print("\n🔍 指纹唯一性分析:")
        print("=" * 50)
        
        # 分析各个组件的唯一性
        components = {
            'userAgent': [],
            'screen_resolution': [],
            'timezone': [],
            'canvas': [],
            'webgl_renderer': [],
            'fonts_count': [],
            'plugins_count': []
        }
        
        for fp in self.fingerprints:
            components['userAgent'].append(fp.get('basic', {}).get('userAgent', ''))
            
            screen = fp.get('screen', {})
            resolution = f"{screen.get('screenWidth', 0)}x{screen.get('screenHeight', 0)}"
            components['screen_resolution'].append(resolution)
            
            components['timezone'].append(fp.get('timezone', {}).get('timezone', ''))
            components['canvas'].append(fp.get('canvas', '')[:50])  # 只取前50字符
            
            webgl = fp.get('webgl', {})
            if isinstance(webgl, dict):
                components['webgl_renderer'].append(webgl.get('renderer', ''))
            else:
                components['webgl_renderer'].append(str(webgl))
            
            components['fonts_count'].append(len(fp.get('fonts', [])))
            components['plugins_count'].append(len(fp.get('plugins', [])))
        
        # 计算每个组件的唯一值数量
        for component, values in components.items():
            unique_count = len(set(values))
            total_count = len(values)
            uniqueness = (unique_count / total_count) * 100 if total_count > 0 else 0
            
            print(f"📊 {component}:")
            print(f"   唯一值: {unique_count}/{total_count} ({uniqueness:.1f}%)")
            
            # 显示最常见的值
            if values:
                counter = Counter(values)
                most_common = counter.most_common(3)
                print(f"   最常见值: {most_common[0][0][:50]}... (出现{most_common[0][1]}次)")
        
        self.analysis_results['uniqueness'] = components
    
    def analyze_entropy(self):
        """分析指纹熵值"""
        print("\n🎲 指纹熵值分析:")
        print("=" * 50)
        
        if not self.fingerprints:
            return
        
        # 计算各组件的信息熵
        import math
        
        def calculate_entropy(values):
            if not values:
                return 0
            
            counter = Counter(values)
            total = len(values)
            entropy = 0
            
            for count in counter.values():
                probability = count / total
                if probability > 0:
                    entropy -= probability * math.log2(probability)
            
            return entropy
        
        components = self.analysis_results.get('uniqueness', {})
        entropies = {}
        
        for component, values in components.items():
            entropy = calculate_entropy(values)
            entropies[component] = entropy
            print(f"📈 {component}: {entropy:.2f} bits")
        
        # 计算总体指纹熵
        total_entropy = sum(entropies.values())
        print(f"\n🎯 总体指纹熵: {total_entropy:.2f} bits")
        print(f"🔢 理论唯一指纹数: 2^{total_entropy:.1f} ≈ {2**total_entropy:.0e}")
        
        self.analysis_results['entropy'] = entropies
    
    def detect_tracking_resistance(self):
        """检测反跟踪措施"""
        print("\n🛡️ 反跟踪措施检测:")
        print("=" * 50)
        
        if not self.fingerprints:
            return
        
        for i, fp in enumerate(self.fingerprints):
            print(f"\n📱 指纹 #{i+1} ({fp.get('hash', 'unknown')[:8]}):")
            
            # 检测常见的反跟踪特征
            basic = fp.get('basic', {})
            
            # DoNotTrack设置
            dnt = basic.get('doNotTrack')
            if dnt == '1':
                print("   ✅ 启用了 Do Not Track")
            else:
                print("   ❌ 未启用 Do Not Track")
            
            # 用户代理分析
            ua = basic.get('userAgent', '')
            if 'Tor' in ua:
                print("   🔒 检测到 Tor 浏览器")
            elif 'Privacy' in ua or 'Private' in ua:
                print("   🔒 检测到隐私浏览器")
            else:
                print("   📊 标准浏览器")
            
            # Canvas指纹检测
            canvas = fp.get('canvas', '')
            if canvas == 'Canvas not supported' or len(canvas) < 100:
                print("   🛡️ Canvas指纹被阻止或修改")
            else:
                print("   📊 Canvas指纹可用")
            
            # WebGL检测
            webgl = fp.get('webgl', {})
            if isinstance(webgl, str) and 'not supported' in webgl:
                print("   🛡️ WebGL被禁用")
            else:
                print("   📊 WebGL可用")
            
            # 字体数量分析
            fonts_count = len(fp.get('fonts', []))
            if fonts_count < 10:
                print(f"   🛡️ 字体数量较少 ({fonts_count}个) - 可能使用了字体限制")
            else:
                print(f"   📊 字体数量正常 ({fonts_count}个)")
    
    def generate_report(self):
        """生成分析报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f'fingerprint_analysis_report_{timestamp}.json'
        
        report = {
            'analysis_time': datetime.now().isoformat(),
            'total_fingerprints': len(self.fingerprints),
            'analysis_results': self.analysis_results,
            'summary': {
                'most_unique_component': '',
                'least_unique_component': '',
                'average_entropy': 0,
                'privacy_score': 0
            }
        }
        
        # 计算摘要信息
        if 'entropy' in self.analysis_results:
            entropies = self.analysis_results['entropy']
            if entropies:
                report['summary']['most_unique_component'] = max(entropies, key=entropies.get)
                report['summary']['least_unique_component'] = min(entropies, key=entropies.get)
                report['summary']['average_entropy'] = sum(entropies.values()) / len(entropies)
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 分析报告已保存: {report_file}")
        return report_file
    
    def visualize_data(self):
        """可视化指纹数据"""
        if not self.fingerprints or 'entropy' not in self.analysis_results:
            print("❌ 没有足够的数据进行可视化")
            return
        
        try:
            import matplotlib.pyplot as plt
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建熵值柱状图
            entropies = self.analysis_results['entropy']
            components = list(entropies.keys())
            values = list(entropies.values())
            
            plt.figure(figsize=(12, 6))
            bars = plt.bar(components, values, color='skyblue', alpha=0.7)
            plt.title('浏览器指纹组件熵值分析', fontsize=16, fontweight='bold')
            plt.xlabel('指纹组件', fontsize=12)
            plt.ylabel('熵值 (bits)', fontsize=12)
            plt.xticks(rotation=45, ha='right')
            
            # 在柱子上显示数值
            for bar, value in zip(bars, values):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                        f'{value:.2f}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            # 保存图表
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            chart_file = f'fingerprint_entropy_chart_{timestamp}.png'
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.show()
            
            print(f"📊 图表已保存: {chart_file}")
            
        except ImportError:
            print("❌ 需要安装 matplotlib 来生成图表: pip install matplotlib")
        except Exception as e:
            print(f"❌ 生成图表失败: {e}")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🔬 浏览器指纹分析器 - 学习版")
        print("=" * 60)
        
        # 加载数据
        if not self.load_all_fingerprints():
            print("❌ 没有找到指纹数据文件")
            return
        
        print(f"📊 已加载 {len(self.fingerprints)} 个指纹样本")
        
        # 执行各项分析
        self.analyze_uniqueness()
        self.analyze_entropy()
        self.detect_tracking_resistance()
        
        # 生成报告和图表
        self.generate_report()
        self.visualize_data()
        
        print("\n🎉 分析完成！")
        print("\n📚 学习要点:")
        print("- 指纹熵值越高，用户越容易被唯一识别")
        print("- 多个低熵组件组合可能产生高唯一性")
        print("- 反跟踪措施可以降低指纹的独特性")
        print("- 了解这些技术有助于保护个人隐私")

def main():
    """主函数"""
    analyzer = FingerprintAnalyzer()
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main()
