# FlexTV Python自动下载器使用指南

## 🎯 功能概述

这个Python自动下载器可以：
- 🤖 **自动操作指纹浏览器**（支持RoxyChrome等）
- 🎬 **自动提取所有剧集视频链接**
- 📥 **批量下载视频文件**
- 🔄 **支持M3U8和MP4格式**
- 🌐 **支持代理和指纹浏览器配置**

## 🚀 快速开始

### 第一步：安装依赖
```bash
# 运行自动安装脚本
python install.py

# 或手动安装
pip install -r requirements.txt
```

### 第二步：配置设置
编辑 `simple_config.json` 文件：
```json
{
  "video_url": "https://www.flextv.cc/video/The_Secret_Recipe_to_Snatch_a_Billionaire-J9zD5pbn1x",
  "download_dir": "downloads",
  "proxy": {
    "enabled": true,
    "host": "127.0.0.1",
    "port": "7890"
  },
  "browser": {
    "headless": false,
    "profile_path": ""
  }
}
```

### 第三步：运行下载器
```bash
# 使用简化版本（推荐）
python run_downloader.py

# 或使用完整版本
python flextv_auto_downloader.py
```

## ⚙️ 详细配置

### 代理配置
```json
{
  "proxy": {
    "enabled": true,
    "host": "127.0.0.1",
    "port": "7890",
    "username": "用户名（可选）",
    "password": "密码（可选）"
  }
}
```

### RoxyChrome配置
```json
{
  "browser": {
    "profile_path": "/path/to/your/roxy/profile",
    "headless": false
  }
}
```

### 指纹浏览器配置
```json
{
  "fingerprint": {
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "window_size": "1920,1080",
    "language": "en-US",
    "timezone": "America/New_York"
  }
}
```

## 📋 工作流程

1. **启动指纹浏览器**
   - 自动配置代理和指纹参数
   - 支持RoxyChrome等专业工具

2. **访问视频页面**
   - 自动打开指定的FlexTV视频页面
   - 等待页面完全加载

3. **注入提取脚本**
   - 自动注入JavaScript提取器
   - 优先使用高级API提取器

4. **提取视频信息**
   - 自动获取所有剧集列表
   - 提取每集的视频下载链接
   - 支持M3U8和MP4格式

5. **保存数据**
   - 自动保存完整数据为JSON文件
   - 单独保存视频链接列表

6. **批量下载**
   - 可选择自动下载所有视频
   - 支持并发下载，提高效率
   - 自动重试失败的下载

## 🎥 下载功能

### 支持的格式
- **M3U8流媒体**：使用FFmpeg自动转换为MP4
- **MP4直链**：直接下载

### 下载配置
```json
{
  "download": {
    "max_concurrent": 3,
    "retry_attempts": 3,
    "timeout": 300
  }
}
```

### 文件命名
- 格式：`Episode_01_剧集标题.mp4`
- 自动处理特殊字符
- 按集数排序

## 🔧 高级功能

### 网络请求拦截
- 自动拦截API请求
- 记录所有视频相关的网络活动
- 用于调试和分析

### 智能重试机制
- 自动重试失败的请求
- 指数退避算法
- 避免被服务器限制

### 并发处理
- 支持多线程下载
- 可配置并发数量
- 自动负载均衡

## 📊 输出文件

### 数据文件
- `flextv_data_YYYYMMDD_HHMMSS.json`：完整提取数据
- `video_links_YYYYMMDD_HHMMSS.json`：纯视频链接
- `flextv_downloader.log`：运行日志

### 视频文件
- 保存在 `downloads` 目录
- 按集数命名：`Episode_01.mp4`, `Episode_02.mp4`...
- 自动创建子目录（如果需要）

## ⚠️ 注意事项

### 网络要求
- 需要稳定的网络连接
- 建议使用代理访问FlexTV
- 确保代理服务器稳定

### 系统要求
- Python 3.7+
- Chrome浏览器
- FFmpeg（用于M3U8下载）
- 足够的磁盘空间

### 法律声明
- 仅用于学习和研究目的
- 请遵守相关法律法规
- 尊重版权和服务条款

## 🛠️ 故障排除

### 问题1：浏览器启动失败
**解决方案**：
```bash
# 检查Chrome是否正确安装
google-chrome --version

# 更新ChromeDriver
pip install --upgrade webdriver-manager
```

### 问题2：代理连接失败
**解决方案**：
- 检查代理服务器是否运行
- 确认代理地址和端口正确
- 测试代理是否可以访问FlexTV

### 问题3：视频下载失败
**解决方案**：
```bash
# 检查FFmpeg安装
ffmpeg -version

# 手动下载FFmpeg
# Windows: https://ffmpeg.org/download.html
# macOS: brew install ffmpeg
# Linux: sudo apt install ffmpeg
```

### 问题4：提取超时
**解决方案**：
- 增加超时时间设置
- 检查网络连接稳定性
- 尝试使用不同的提取脚本

## 📈 性能优化

### 提高下载速度
```json
{
  "download": {
    "max_concurrent": 5,
    "chunk_size": 16384
  }
}
```

### 减少内存使用
```json
{
  "browser": {
    "headless": true,
    "disable_images": true
  }
}
```

### 避免被限制
```json
{
  "extraction": {
    "delay_between_requests": 3,
    "max_concurrent_episodes": 2
  }
}
```

## 🎉 使用示例

### 基本使用
```bash
# 1. 安装依赖
python install.py

# 2. 编辑配置
# 修改 simple_config.json 中的video_url和代理设置

# 3. 运行下载器
python run_downloader.py

# 4. 等待完成
# 视频将自动下载到 downloads 目录
```

### 高级使用
```bash
# 使用完整配置
python flextv_auto_downloader.py config.json

# 指定RoxyChrome配置
# 在config.json中设置profile_path

# 批量处理多个视频
# 修改脚本中的video_url列表
```

## 💡 最佳实践

1. **首次使用**：先运行测试脚本确认环境
2. **代理设置**：使用稳定的代理服务器
3. **存储空间**：确保有足够的磁盘空间
4. **网络稳定**：在网络稳定时运行下载
5. **定期更新**：保持依赖包和浏览器最新

通过这个Python自动下载器，您可以轻松实现FlexTV视频的批量下载，无需手动操作浏览器！
