#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlexTV 自动下载器 - 使用指纹浏览器自动获取和下载视频
支持 RoxyChrome 等指纹浏览器
"""

import os
import json
import time
import requests
import subprocess
import logging
import argparse
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from urllib.parse import urlparse, urljoin
import threading
from concurrent.futures import ThreadPoolExecutor
import m3u8
import ffmpeg
from webdriver_manager.chrome import ChromeDriverManager

class FlexTVAutoDownloader:
    def __init__(self, config_file="config.json"):
        self.driver = None
        self.config = self.load_config(config_file)
        self.download_dir = self.config['download']['directory']
        self.extracted_data = {}
        self.video_links = []
        self.logger = self.setup_logging()

        # 创建下载目录
        os.makedirs(self.download_dir, exist_ok=True)
        self.logger.info(f"下载目录已创建: {self.download_dir}")

    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件加载成功: {config_file}")
            return config
        except FileNotFoundError:
            print(f"⚠️ 配置文件不存在，使用默认配置: {config_file}")
            return self.get_default_config()
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e}")
            return self.get_default_config()

    def get_default_config(self):
        """获取默认配置"""
        return {
            "proxy": {"enabled": False},
            "fingerprint": {
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "window_size": "1920,1080",
                "language": "en-US"
            },
            "browser": {"headless": False, "page_load_timeout": 30},
            "download": {"directory": "downloads", "max_concurrent": 3, "retry_attempts": 3},
            "extraction": {"timeout": 300, "retry_attempts": 3},
            "logging": {"level": "INFO", "file": "flextv_downloader.log"}
        }

    def setup_logging(self):
        """设置日志"""
        log_config = self.config['logging']
        logging.basicConfig(
            level=getattr(logging, log_config['level']),
            format=log_config.get('format', '%(asctime)s - %(levelname)s - %(message)s'),
            handlers=[
                logging.FileHandler(log_config['file'], encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
        
    def setup_browser(self, profile_path=None):
        """设置指纹浏览器"""
        chrome_options = Options()
        
        # 指纹浏览器配置
        if self.fingerprint_config:
            # 设置用户代理
            if 'user_agent' in self.fingerprint_config:
                chrome_options.add_argument(f"--user-agent={self.fingerprint_config['user_agent']}")
            
            # 设置窗口大小
            if 'window_size' in self.fingerprint_config:
                chrome_options.add_argument(f"--window-size={self.fingerprint_config['window_size']}")
            
            # 设置语言
            if 'language' in self.fingerprint_config:
                chrome_options.add_argument(f"--lang={self.fingerprint_config['language']}")
        
        # 代理配置
        if self.proxy_config:
            proxy_string = f"{self.proxy_config['host']}:{self.proxy_config['port']}"
            chrome_options.add_argument(f"--proxy-server={proxy_string}")
            
            if 'username' in self.proxy_config and 'password' in self.proxy_config:
                # 处理需要认证的代理
                chrome_options.add_extension(self.create_proxy_auth_extension())
        
        # 其他浏览器选项
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 如果指定了profile路径（RoxyChrome等）
        if profile_path:
            chrome_options.add_argument(f"--user-data-dir={profile_path}")
        
        # 启用网络日志
        chrome_options.add_argument("--enable-logging")
        chrome_options.add_argument("--log-level=0")
        chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("✅ 浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            return False
    
    def create_proxy_auth_extension(self):
        """创建代理认证扩展"""
        import zipfile
        import tempfile
        
        manifest_json = """
        {
            "version": "1.0.0",
            "manifest_version": 2,
            "name": "Chrome Proxy",
            "permissions": [
                "proxy",
                "tabs",
                "unlimitedStorage",
                "storage",
                "<all_urls>",
                "webRequest",
                "webRequestBlocking"
            ],
            "background": {
                "scripts": ["background.js"]
            },
            "minimum_chrome_version":"22.0.0"
        }
        """
        
        background_js = f"""
        var config = {{
            mode: "fixed_servers",
            rules: {{
                singleProxy: {{
                    scheme: "http",
                    host: "{self.proxy_config['host']}",
                    port: parseInt({self.proxy_config['port']})
                }},
                bypassList: ["localhost"]
            }}
        }};
        
        chrome.proxy.settings.set({{value: config, scope: "regular"}}, function() {{}});
        
        function callbackFn(details) {{
            return {{
                authCredentials: {{
                    username: "{self.proxy_config['username']}",
                    password: "{self.proxy_config['password']}"
                }}
            }};
        }}
        
        chrome.webRequest.onAuthRequired.addListener(
            callbackFn,
            {{urls: ["<all_urls>"]}},
            ['blocking']
        );
        """
        
        # 创建临时扩展文件
        temp_dir = tempfile.mkdtemp()
        extension_path = os.path.join(temp_dir, "proxy_auth_extension.zip")
        
        with zipfile.ZipFile(extension_path, 'w') as zf:
            zf.writestr("manifest.json", manifest_json)
            zf.writestr("background.js", background_js)
        
        return extension_path
    
    def inject_extractor_script(self):
        """注入视频提取脚本"""
        # 读取高级提取器脚本
        try:
            with open('flextv_advanced_extractor.js', 'r', encoding='utf-8') as f:
                extractor_script = f.read()
        except FileNotFoundError:
            print("❌ 找不到提取器脚本文件")
            return False
        
        try:
            # 注入脚本
            self.driver.execute_script(extractor_script)
            print("✅ 提取器脚本注入成功")
            return True
        except Exception as e:
            print(f"❌ 脚本注入失败: {e}")
            return False
    
    def wait_for_extraction_complete(self, timeout=300):
        """等待提取完成"""
        print("⏳ 等待视频信息提取完成...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 检查是否有提取结果
                result = self.driver.execute_script("return window.flextv_advanced_data;")
                if result:
                    self.extracted_data = result
                    print(f"✅ 提取完成！找到 {result.get('totalEpisodes', 0)} 集")
                    print(f"🎥 成功提取 {result.get('successfulExtractions', 0)} 集视频链接")
                    return True
                
                time.sleep(2)
            except Exception as e:
                print(f"⚠️ 检查提取状态时出错: {e}")
                time.sleep(5)
        
        print("❌ 提取超时")
        return False
    
    def extract_video_links(self):
        """提取视频链接"""
        if not self.extracted_data:
            return []
        
        video_links = []
        video_data = self.extracted_data.get('videoData', [])
        
        for episode in video_data:
            if episode.get('success') and episode.get('videoLinks'):
                for link in episode['videoLinks']:
                    video_links.append({
                        'episode': episode['episodeNumber'],
                        'title': episode.get('episodeTitle', f"Episode {episode['episodeNumber']}"),
                        'url': link['url'],
                        'type': link['type'],
                        'quality': link.get('quality', 'unknown')
                    })
        
        self.video_links = video_links
        return video_links
    
    def download_video(self, video_info, max_retries=3):
        """下载单个视频"""
        episode_num = video_info['episode']
        title = video_info['title']
        url = video_info['url']
        video_type = video_info['type']
        
        # 创建文件名
        safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"Episode_{episode_num:02d}_{safe_title}"
        
        print(f"📥 开始下载第 {episode_num} 集: {title}")
        
        try:
            if video_type == 'hls' or url.endswith('.m3u8'):
                # 下载M3U8流
                return self.download_m3u8(url, filename, max_retries)
            else:
                # 下载MP4文件
                return self.download_mp4(url, filename, max_retries)
        except Exception as e:
            print(f"❌ 下载第 {episode_num} 集失败: {e}")
            return False
    
    def download_m3u8(self, url, filename, max_retries=3):
        """下载M3U8流媒体"""
        output_path = os.path.join(self.download_dir, f"{filename}.mp4")
        
        for attempt in range(max_retries):
            try:
                print(f"🎬 下载M3U8流 (尝试 {attempt + 1}/{max_retries})")
                
                # 使用ffmpeg下载M3U8
                (
                    ffmpeg
                    .input(url, headers=self.get_request_headers())
                    .output(output_path, vcodec='copy', acodec='copy')
                    .overwrite_output()
                    .run(capture_stdout=True, capture_stderr=True)
                )
                
                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    print(f"✅ M3U8下载成功: {output_path}")
                    return True
                    
            except Exception as e:
                print(f"⚠️ M3U8下载失败 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
        
        return False
    
    def download_mp4(self, url, filename, max_retries=3):
        """下载MP4文件"""
        output_path = os.path.join(self.download_dir, f"{filename}.mp4")
        
        for attempt in range(max_retries):
            try:
                print(f"📹 下载MP4文件 (尝试 {attempt + 1}/{max_retries})")
                
                response = requests.get(url, headers=self.get_request_headers(), stream=True)
                response.raise_for_status()
                
                with open(output_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    print(f"✅ MP4下载成功: {output_path}")
                    return True
                    
            except Exception as e:
                print(f"⚠️ MP4下载失败 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
        
        return False
    
    def get_request_headers(self):
        """获取请求头"""
        return {
            'User-Agent': self.driver.execute_script("return navigator.userAgent;") if self.driver else 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://www.flextv.cc/',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
    
    def batch_download(self, max_workers=3):
        """批量下载视频"""
        if not self.video_links:
            print("❌ 没有可下载的视频链接")
            return
        
        print(f"🚀 开始批量下载 {len(self.video_links)} 个视频...")
        
        success_count = 0
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(self.download_video, video) for video in self.video_links]
            
            for future in futures:
                if future.result():
                    success_count += 1
        
        print(f"🎉 下载完成！成功: {success_count}/{len(self.video_links)}")
    
    def save_extracted_data(self):
        """保存提取的数据"""
        if self.extracted_data:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"flextv_data_{timestamp}.json"
            filepath = os.path.join(self.download_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 数据已保存: {filepath}")
    
    def run(self, video_url, profile_path=None):
        """运行完整的提取和下载流程"""
        print("🚀 FlexTV 自动下载器启动...")
        
        # 1. 启动浏览器
        if not self.setup_browser(profile_path):
            return False
        
        try:
            # 2. 访问视频页面
            print(f"🌐 访问视频页面: {video_url}")
            self.driver.get(video_url)
            time.sleep(5)
            
            # 3. 注入提取脚本
            if not self.inject_extractor_script():
                return False
            
            # 4. 等待提取完成
            if not self.wait_for_extraction_complete():
                return False
            
            # 5. 提取视频链接
            video_links = self.extract_video_links()
            print(f"🎥 提取到 {len(video_links)} 个视频链接")
            
            # 6. 保存数据
            self.save_extracted_data()
            
            # 7. 批量下载
            if video_links:
                download_choice = input("是否开始下载视频？(y/n): ").lower()
                if download_choice == 'y':
                    self.batch_download()
            
            return True
            
        except Exception as e:
            print(f"❌ 运行过程中出错: {e}")
            return False
        
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")

def main():
    """主函数"""
    print("🎬 FlexTV 自动下载器")
    print("=" * 50)
    
    # 配置示例
    proxy_config = {
        'host': '127.0.0.1',
        'port': '7890',
        # 'username': 'your_username',  # 如果代理需要认证
        # 'password': 'your_password'
    }
    
    fingerprint_config = {
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'window_size': '1920,1080',
        'language': 'en-US'
    }
    
    # 视频URL
    video_url = "https://www.flextv.cc/video/The_Secret_Recipe_to_Snatch_a_Billionaire-J9zD5pbn1x"
    
    # RoxyChrome配置路径（如果使用）
    # profile_path = "/path/to/your/roxy/profile"
    profile_path = None
    
    # 创建下载器实例
    downloader = FlexTVAutoDownloader(
        proxy_config=proxy_config,
        fingerprint_config=fingerprint_config
    )
    
    # 运行下载流程
    success = downloader.run(video_url, profile_path)
    
    if success:
        print("🎉 任务完成！")
    else:
        print("❌ 任务失败！")

if __name__ == "__main__":
    main()
