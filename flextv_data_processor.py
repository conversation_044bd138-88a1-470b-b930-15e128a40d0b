#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlexTV 数据处理器
处理从浏览器提取的视频数据，解析剧集信息和下载链接
"""

import json
import os
import re
import base64
import requests
from urllib.parse import urljoin, urlparse
import csv
from datetime import datetime

class FlexTVDataProcessor:
    def __init__(self, data_file=None):
        self.data = None
        self.processed_episodes = []
        self.video_links = []
        
        if data_file and os.path.exists(data_file):
            self.load_data(data_file)
    
    def load_data(self, file_path):
        """加载从浏览器提取的JSON数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ 成功加载数据文件: {file_path}")
            return True
        except Exception as e:
            print(f"❌ 加载数据文件失败: {e}")
            return False
    
    def analyze_data(self):
        """分析提取的数据"""
        if not self.data:
            print("❌ 没有数据可分析")
            return
        
        print("📊 数据分析结果:")
        print(f"   标题: {self.data.get('title', 'Unknown')}")
        print(f"   Series ID: {self.data.get('seriesId', 'Unknown')}")
        print(f"   剧集数量: {len(self.data.get('episodes', []))}")
        print(f"   视频源数量: {len(self.data.get('videoSources', []))}")
        print(f"   API请求数量: {len(self.data.get('networkRequests', []))}")
        print(f"   提取时间: {self.data.get('extractTime', 'Unknown')}")
        
        # 分析剧集信息
        episodes = self.data.get('episodes', [])
        if episodes:
            print(f"\n📺 剧集列表:")
            for i, episode in enumerate(episodes[:10]):  # 只显示前10集
                print(f"   第{episode.get('number', i+1)}集: {episode.get('title', 'No Title')}")
                if episode.get('link'):
                    print(f"      链接: {episode['link']}")
            
            if len(episodes) > 10:
                print(f"   ... 还有 {len(episodes) - 10} 集")
        
        # 分析视频源
        video_sources = self.data.get('videoSources', [])
        if video_sources:
            print(f"\n🎥 视频源:")
            for i, source in enumerate(video_sources):
                print(f"   源 {i+1}: {source.get('src', 'No Source')}")
                if source.get('sources'):
                    for sub_source in source['sources']:
                        print(f"      - {sub_source.get('src', '')} ({sub_source.get('type', '')})")
        
        # 分析API请求
        api_requests = self.data.get('networkRequests', [])
        if api_requests:
            print(f"\n🌐 API请求:")
            for request in api_requests[:5]:  # 只显示前5个
                print(f"   {request.get('type', '').upper()}: {request.get('url', '')}")
    
    def extract_video_links(self):
        """提取所有可能的视频下载链接"""
        video_links = []
        
        if not self.data:
            return video_links
        
        # 1. 从视频源提取
        video_sources = self.data.get('videoSources', [])
        for source in video_sources:
            if source.get('src'):
                video_links.append({
                    'type': 'direct_video',
                    'url': source['src'],
                    'source': 'video_element'
                })
            
            for sub_source in source.get('sources', []):
                if sub_source.get('src'):
                    video_links.append({
                        'type': 'video_source',
                        'url': sub_source['src'],
                        'mime_type': sub_source.get('type', ''),
                        'source': 'source_element'
                    })
        
        # 2. 从网络请求提取
        network_requests = self.data.get('networkRequests', [])
        for request in network_requests:
            url = request.get('url', '')
            if any(ext in url.lower() for ext in ['.mp4', '.m3u8', '.webm', '.avi', '.mov']):
                video_links.append({
                    'type': 'network_request',
                    'url': url,
                    'request_type': request.get('type', ''),
                    'source': 'network_intercept'
                })
        
        # 3. 从API数据提取
        api_results = self.data.get('apiResults', [])
        for result in api_results:
            if result.get('success') and result.get('data'):
                # 这里可以添加解密逻辑
                data = result['data']
                if isinstance(data, dict) and 'video_url' in str(data):
                    # 尝试提取视频URL
                    video_links.append({
                        'type': 'api_response',
                        'url': str(data),
                        'source': 'api_data'
                    })
        
        # 4. 从加密数据提取
        encrypted_data = self.data.get('encryptedData', [])
        for data_item in encrypted_data:
            content = data_item.get('content', '')
            # 使用正则表达式查找可能的视频URL
            video_urls = re.findall(r'https?://[^\s"\'<>]+\.(?:mp4|m3u8|webm|avi|mov)', content)
            for url in video_urls:
                video_links.append({
                    'type': 'encrypted_extract',
                    'url': url,
                    'source': 'script_content'
                })
        
        self.video_links = video_links
        return video_links
    
    def process_episodes(self):
        """处理剧集信息"""
        if not self.data:
            return []
        
        episodes = self.data.get('episodes', [])
        processed = []
        
        for episode in episodes:
            processed_episode = {
                'number': episode.get('number', 0),
                'title': episode.get('title', ''),
                'link': episode.get('link', ''),
                'thumbnail': episode.get('thumbnail', ''),
                'is_active': episode.get('isActive', False),
                'series_id': self.data.get('seriesId', ''),
                'series_title': self.data.get('title', '')
            }
            
            # 尝试从链接中提取更多信息
            if processed_episode['link']:
                parsed_url = urlparse(processed_episode['link'])
                processed_episode['episode_id'] = parsed_url.path.split('/')[-1] if parsed_url.path else ''
            
            processed.append(processed_episode)
        
        self.processed_episodes = processed
        return processed
    
    def save_results(self, output_dir='flextv_output'):
        """保存处理结果"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        series_id = self.data.get('seriesId', 'unknown') if self.data else 'unknown'
        
        # 保存剧集信息
        if self.processed_episodes:
            episodes_file = os.path.join(output_dir, f'episodes_{series_id}_{timestamp}.json')
            with open(episodes_file, 'w', encoding='utf-8') as f:
                json.dump(self.processed_episodes, f, ensure_ascii=False, indent=2)
            print(f"📁 剧集信息已保存: {episodes_file}")
            
            # 保存为CSV
            csv_file = os.path.join(output_dir, f'episodes_{series_id}_{timestamp}.csv')
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                if self.processed_episodes:
                    writer = csv.DictWriter(f, fieldnames=self.processed_episodes[0].keys())
                    writer.writeheader()
                    writer.writerows(self.processed_episodes)
            print(f"📊 CSV文件已保存: {csv_file}")
        
        # 保存视频链接
        if self.video_links:
            links_file = os.path.join(output_dir, f'video_links_{series_id}_{timestamp}.json')
            with open(links_file, 'w', encoding='utf-8') as f:
                json.dump(self.video_links, f, ensure_ascii=False, indent=2)
            print(f"🎥 视频链接已保存: {links_file}")
        
        # 保存完整分析报告
        report = {
            'analysis_time': datetime.now().isoformat(),
            'series_info': {
                'title': self.data.get('title', '') if self.data else '',
                'series_id': series_id,
                'total_episodes': len(self.processed_episodes),
                'page_url': self.data.get('pageUrl', '') if self.data else ''
            },
            'episodes': self.processed_episodes,
            'video_links': self.video_links,
            'extraction_summary': {
                'episodes_found': len(self.processed_episodes),
                'video_links_found': len(self.video_links),
                'direct_video_sources': len([l for l in self.video_links if l['type'] == 'direct_video']),
                'network_requests': len([l for l in self.video_links if l['type'] == 'network_request'])
            }
        }
        
        report_file = os.path.join(output_dir, f'analysis_report_{series_id}_{timestamp}.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"📋 分析报告已保存: {report_file}")
        
        return output_dir
    
    def run_analysis(self):
        """运行完整分析"""
        if not self.data:
            print("❌ 没有数据可分析，请先加载数据文件")
            return
        
        print("🚀 开始分析FlexTV数据...")
        
        # 1. 基本分析
        self.analyze_data()
        
        # 2. 处理剧集
        episodes = self.process_episodes()
        print(f"\n✅ 处理了 {len(episodes)} 个剧集")
        
        # 3. 提取视频链接
        video_links = self.extract_video_links()
        print(f"✅ 提取了 {len(video_links)} 个视频链接")
        
        # 4. 保存结果
        output_dir = self.save_results()
        print(f"\n🎉 分析完成！结果保存在: {output_dir}")
        
        return {
            'episodes': episodes,
            'video_links': video_links,
            'output_dir': output_dir
        }

def main():
    """主函数"""
    print("🎬 FlexTV 数据处理器")
    print("=" * 50)
    
    # 查找当前目录下的JSON文件
    json_files = [f for f in os.listdir('.') if f.endswith('.json') and 'flextv' in f.lower()]
    
    if not json_files:
        print("❌ 未找到FlexTV数据文件")
        print("请确保已从浏览器提取数据并保存为JSON文件")
        return
    
    print(f"📁 找到 {len(json_files)} 个数据文件:")
    for i, file in enumerate(json_files):
        print(f"   {i+1}. {file}")
    
    # 处理所有文件
    for file in json_files:
        print(f"\n🔄 处理文件: {file}")
        processor = FlexTVDataProcessor(file)
        if processor.data:
            processor.run_analysis()
        print("-" * 50)

if __name__ == "__main__":
    main()
