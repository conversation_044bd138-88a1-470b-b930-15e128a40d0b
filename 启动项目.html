<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器指纹学术研究工具 - 启动页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            width: 90%;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature h3 {
            font-size: 1.3em;
            margin-bottom: 10px;
            color: #fff;
        }
        
        .feature p {
            opacity: 0.8;
            line-height: 1.5;
        }
        
        .controls {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 50px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }
        
        .btn.success {
            background: linear-gradient(45deg, #00b894, #00a085);
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            display: none;
        }
        
        .status.show {
            display: block;
        }
        
        .console-output {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            display: none;
        }
        
        .console-output.show {
            display: block;
        }
        
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .warning h4 {
            color: #ffc107;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 浏览器指纹学术研究工具</h1>
            <p>Browser Fingerprint & Video API Academic Research Tool</p>
        </div>
        
        <div class="warning">
            <h4>⚠️ 学术研究声明</h4>
            <p>本工具仅用于学术研究和教育目的。请遵守相关法律法规和道德准则，不得用于恶意跟踪或隐私侵犯。</p>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🔍 指纹收集</h3>
                <p>收集完整的浏览器指纹信息，包括Canvas、WebGL、音频指纹等高级特征。</p>
            </div>
            <div class="feature">
                <h3>🌐 网络监控</h3>
                <p>实时拦截和分析所有网络请求，识别API调用模式和数据流。</p>
            </div>
            <div class="feature">
                <h3>🎥 视频分析</h3>
                <p>专业的视频API分析，自动提取媒体链接和剧集信息。</p>
            </div>
            <div class="feature">
                <h3>📊 数据导出</h3>
                <p>生成详细的JSON报告，便于后续的学术分析和研究。</p>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="startResearch()">🚀 启动研究工具</button>
            <button class="btn secondary" onclick="loadTestPage()">🧪 加载测试页面</button>
            <button class="btn success" onclick="openDocumentation()">📚 查看文档</button>
        </div>
        
        <div class="status" id="status">
            <h4>📊 工具状态</h4>
            <p id="statusText">准备就绪</p>
        </div>
        
        <div class="console-output" id="console">
            <div id="consoleContent">控制台输出将在这里显示...</div>
        </div>
    </div>

    <script>
        // 控制台输出重定向
        const originalLog = console.log;
        const consoleDiv = document.getElementById('consoleContent');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const timestamp = new Date().toLocaleTimeString();
            consoleDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        function showStatus(text, show = true) {
            const status = document.getElementById('status');
            const statusText = document.getElementById('statusText');
            statusText.textContent = text;
            if (show) {
                status.classList.add('show');
            } else {
                status.classList.remove('show');
            }
        }
        
        function showConsole() {
            document.getElementById('console').classList.add('show');
        }
        
        async function startResearch() {
            showStatus('正在启动研究工具...');
            showConsole();
            
            console.log('🎓 启动浏览器指纹学术研究工具...');
            console.log('📋 加载核心模块...');
            
            try {
                // 动态加载研究工具脚本
                const response = await fetch('./browser_fingerprint_collector.js');
                const scriptContent = await response.text();
                
                console.log('✅ 核心模块加载成功');
                console.log('🔧 初始化指纹收集器...');
                
                // 执行脚本
                eval(scriptContent);
                
                showStatus('研究工具已启动，请查看控制台输出');
                console.log('🎉 研究工具启动完成！');
                console.log('📊 数据收集已开始，请等待分析结果...');
                
            } catch (error) {
                console.error('❌ 启动失败:', error);
                showStatus('启动失败，请检查文件是否存在');
            }
        }
        
        function loadTestPage() {
            showStatus('正在加载测试页面...');
            console.log('🧪 加载测试页面...');
            
            // 创建测试页面
            const testWindow = window.open('', '_blank', 'width=800,height=600');
            testWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>测试页面 - 学术研究</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                        video { width: 100%; max-width: 500px; }
                    </style>
                </head>
                <body>
                    <h1>🧪 学术研究测试页面</h1>
                    <div class="test-section">
                        <h3>视频元素测试</h3>
                        <video controls>
                            <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                            您的浏览器不支持视频标签。
                        </video>
                    </div>
                    <div class="test-section">
                        <h3>Canvas测试</h3>
                        <canvas id="testCanvas" width="200" height="100" style="border:1px solid #000;"></canvas>
                    </div>
                    <div class="test-section">
                        <h3>API测试</h3>
                        <button onclick="testAPI()">发送测试API请求</button>
                        <div id="apiResult"></div>
                    </div>
                    
                    <script>
                        // Canvas测试
                        const canvas = document.getElementById('testCanvas');
                        const ctx = canvas.getContext('2d');
                        ctx.fillStyle = '#FF0000';
                        ctx.fillRect(0, 0, 100, 50);
                        ctx.fillStyle = '#00FF00';
                        ctx.fillRect(50, 25, 100, 50);
                        ctx.font = '16px Arial';
                        ctx.fillStyle = '#0000FF';
                        ctx.fillText('Test Canvas', 10, 80);
                        
                        // API测试
                        function testAPI() {
                            fetch('https://jsonplaceholder.typicode.com/posts/1')
                                .then(response => response.json())
                                .then(data => {
                                    document.getElementById('apiResult').innerHTML = 
                                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                                })
                                .catch(error => {
                                    document.getElementById('apiResult').innerHTML = 
                                        '<p style="color:red;">API请求失败: ' + error + '</p>';
                                });
                        }
                        
                        console.log('🧪 测试页面已加载');
                    </script>
                </body>
                </html>
            `);
            
            showStatus('测试页面已在新窗口中打开');
            console.log('✅ 测试页面已创建，可以在新窗口中运行研究工具');
        }
        
        function openDocumentation() {
            showStatus('正在打开文档...');
            console.log('📚 打开使用文档...');
            
            // 尝试打开文档文件
            window.open('./学术研究工具使用指南.md', '_blank');
            
            showStatus('文档已在新标签页中打开');
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('🎓 浏览器指纹学术研究工具启动页面已加载');
            console.log('📋 请点击"启动研究工具"开始数据收集');
            console.log('⚠️ 提醒：本工具仅用于学术研究目的');
        });
    </script>
</body>
</html>
