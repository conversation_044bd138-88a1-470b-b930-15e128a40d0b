// FlexTV 全集视频提取器 - 自动获取所有剧集的视频下载链接
// 在视频页面的浏览器控制台中运行

(function () {
    'use strict';

    console.log('🎭 FlexTV 全集视频提取器启动...');

    class FlexTVExtractor {
        constructor() {
            this.seriesId = this.extractSeriesId();
            this.episodes = [];
            this.videoSources = [];
            this.apiRequests = [];
            this.allEpisodesData = [];
            this.currentEpisodeIndex = 0;
            this.maxRetries = 3;
            this.delayBetweenRequests = 2000; // 2秒延迟避免被限制
            this.setupNetworkInterception();
        }

        // 从URL提取series_id
        extractSeriesId() {
            const urlMatch = window.location.pathname.match(/([^-]+)$/);
            return urlMatch ? urlMatch[1] : null;
        }

        // 设置网络请求拦截
        setupNetworkInterception() {
            const self = this;
            const originalFetch = window.fetch;

            window.fetch = function (...args) {
                const url = args[0];
                if (typeof url === 'string') {
                    self.logApiRequest('fetch', url);
                }
                return originalFetch.apply(this, args);
            };
        }

        // 记录API请求
        logApiRequest(type, url) {
            if (url.includes('api-quick.flextv.cc') ||
                url.includes('video') ||
                url.includes('m3u8') ||
                url.includes('mp4')) {

                this.apiRequests.push({
                    type,
                    url,
                    timestamp: Date.now()
                });
                console.log(`🌐 [${type.toUpperCase()}] ${url}`);
            }
        }

        // 提取页面中的剧集信息
        extractEpisodesFromPage() {
            const episodes = [];

            // 查找剧集编号按钮或链接
            const episodeSelectors = [
                '[class*="episode"]',
                '[class*="section"]',
                '.episode-item',
                '.section-item',
                'button[class*="episode"]',
                'a[href*="episode"]',
                '.drama_list .item'
            ];

            episodeSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach((element, index) => {
                    const episode = this.parseEpisodeElement(element, index);
                    if (episode) {
                        episodes.push(episode);
                    }
                });
            });

            // 去重
            const uniqueEpisodes = episodes.filter((episode, index, self) =>
                index === self.findIndex(e => e.number === episode.number || e.link === episode.link)
            );

            this.episodes = uniqueEpisodes;
            console.log(`📺 找到 ${this.episodes.length} 集`);
            return this.episodes;
        }

        // 解析单个剧集元素
        parseEpisodeElement(element, index) {
            const episode = {
                number: index + 1,
                title: '',
                link: '',
                thumbnail: '',
                isActive: false
            };

            // 提取标题
            const titleSelectors = ['.title', 'h3', '.episode-title', '.section-title'];
            for (const selector of titleSelectors) {
                const titleEl = element.querySelector(selector);
                if (titleEl) {
                    episode.title = titleEl.textContent.trim();
                    break;
                }
            }

            // 提取链接
            const linkEl = element.querySelector('a') || (element.tagName === 'A' ? element : null);
            if (linkEl) {
                episode.link = linkEl.href;
            }

            // 提取缩略图
            const imgEl = element.querySelector('img');
            if (imgEl) {
                episode.thumbnail = imgEl.src;
            }

            // 检查是否为当前激活的剧集
            episode.isActive = element.classList.contains('active') ||
                element.classList.contains('current') ||
                element.classList.contains('selected');

            // 尝试从文本中提取剧集编号
            const text = element.textContent.trim();
            const numberMatch = text.match(/(\d+)/);
            if (numberMatch) {
                episode.number = parseInt(numberMatch[1]);
            }

            return episode.title || episode.link ? episode : null;
        }

        // 尝试通过API获取剧集数据
        async fetchEpisodesFromAPI() {
            if (!this.seriesId) {
                console.log('❌ 无法获取 series_id');
                return [];
            }

            const apiEndpoints = [
                `https://api-quick.flextv.cc/webGetSeriesSectionFullList?series_id=${this.seriesId}&series_no=1`,
                `https://api-quick.flextv.cc/webGetSeriesSectionFullList?series_id=${this.seriesId}&series_no=-1`,
                `https://api-quick.flextv.cc/webGetSeriesLang?series_id=${this.seriesId}`
            ];

            const results = [];

            for (const url of apiEndpoints) {
                try {
                    console.log(`🔍 尝试API: ${url}`);
                    const response = await fetch(url);
                    const data = await response.json();

                    results.push({
                        url,
                        data,
                        success: data.code === 0
                    });

                    console.log(`📊 API响应:`, data);

                    // 如果数据被加密，尝试解密
                    if (data.data && typeof data.data === 'string' && data.data.length > 100) {
                        console.log('🔐 检测到加密数据，长度:', data.data.length);
                    }

                } catch (error) {
                    console.log(`❌ API请求失败: ${error.message}`);
                    results.push({
                        url,
                        error: error.message,
                        success: false
                    });
                }
            }

            return results;
        }

        // 查找视频源
        findVideoSources() {
            const sources = [];

            // 查找video标签
            const videos = document.querySelectorAll('video');
            videos.forEach((video, index) => {
                const videoData = {
                    index,
                    element: 'video',
                    src: video.src || video.currentSrc,
                    sources: []
                };

                const sourceTags = video.querySelectorAll('source');
                sourceTags.forEach(source => {
                    videoData.sources.push({
                        src: source.src,
                        type: source.type
                    });
                });

                if (videoData.src || videoData.sources.length > 0) {
                    sources.push(videoData);
                }
            });

            // 查找iframe中的视频
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach((iframe, index) => {
                if (iframe.src && (iframe.src.includes('video') || iframe.src.includes('player'))) {
                    sources.push({
                        index,
                        element: 'iframe',
                        src: iframe.src,
                        sources: []
                    });
                }
            });

            this.videoSources = sources;
            console.log(`🎥 找到 ${sources.length} 个视频源`);
            return sources;
        }

        // 延迟函数
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 获取单集的视频链接
        async fetchEpisodeVideoLinks(episodeUrl, episodeNumber) {
            console.log(`🎬 正在获取第 ${episodeNumber} 集视频链接...`);

            try {
                // 创建一个隐藏的iframe来加载剧集页面
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.style.width = '0';
                iframe.style.height = '0';
                document.body.appendChild(iframe);

                return new Promise((resolve, reject) => {
                    let timeout;
                    let videoLinks = [];

                    // 设置超时
                    timeout = setTimeout(() => {
                        document.body.removeChild(iframe);
                        resolve({
                            episodeNumber,
                            episodeUrl,
                            videoLinks: [],
                            error: 'timeout'
                        });
                    }, 15000); // 15秒超时

                    iframe.onload = async () => {
                        try {
                            await this.delay(3000); // 等待页面加载完成

                            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                            // 在iframe中查找视频源
                            const videoElements = iframeDoc.querySelectorAll('video source, video');
                            videoElements.forEach(video => {
                                const src = video.src || video.getAttribute('src');
                                if (src && (src.includes('.m3u8') || src.includes('.mp4'))) {
                                    videoLinks.push({
                                        type: 'video_element',
                                        url: src,
                                        quality: video.getAttribute('data-quality') || 'unknown'
                                    });
                                }
                            });

                            // 查找可能的API调用
                            const scripts = iframeDoc.querySelectorAll('script');
                            scripts.forEach(script => {
                                const content = script.textContent || script.innerHTML;
                                const m3u8Matches = content.match(/https?:\/\/[^\s"']+\.m3u8[^\s"']*/g);
                                const mp4Matches = content.match(/https?:\/\/[^\s"']+\.mp4[^\s"']*/g);

                                if (m3u8Matches) {
                                    m3u8Matches.forEach(url => {
                                        videoLinks.push({
                                            type: 'script_m3u8',
                                            url: url.replace(/['"]/g, ''),
                                            quality: 'unknown'
                                        });
                                    });
                                }

                                if (mp4Matches) {
                                    mp4Matches.forEach(url => {
                                        videoLinks.push({
                                            type: 'script_mp4',
                                            url: url.replace(/['"]/g, ''),
                                            quality: 'unknown'
                                        });
                                    });
                                }
                            });

                            clearTimeout(timeout);
                            document.body.removeChild(iframe);

                            resolve({
                                episodeNumber,
                                episodeUrl,
                                videoLinks: videoLinks,
                                success: true
                            });

                        } catch (error) {
                            clearTimeout(timeout);
                            document.body.removeChild(iframe);
                            resolve({
                                episodeNumber,
                                episodeUrl,
                                videoLinks: [],
                                error: error.message
                            });
                        }
                    };

                    iframe.onerror = () => {
                        clearTimeout(timeout);
                        document.body.removeChild(iframe);
                        resolve({
                            episodeNumber,
                            episodeUrl,
                            videoLinks: [],
                            error: 'iframe_load_error'
                        });
                    };

                    iframe.src = episodeUrl;
                });

            } catch (error) {
                console.error(`❌ 获取第 ${episodeNumber} 集失败:`, error);
                return {
                    episodeNumber,
                    episodeUrl,
                    videoLinks: [],
                    error: error.message
                };
            }
        }

        // 批量获取所有剧集的视频链接
        async fetchAllEpisodesVideoLinks() {
            if (this.episodes.length === 0) {
                console.log('❌ 没有找到剧集信息');
                return [];
            }

            console.log(`🚀 开始获取 ${this.episodes.length} 集的视频链接...`);

            const results = [];
            const totalEpisodes = this.episodes.length;

            for (let i = 0; i < totalEpisodes; i++) {
                const episode = this.episodes[i];
                if (!episode.link) {
                    console.log(`⚠️ 第 ${episode.number} 集没有链接，跳过`);
                    continue;
                }

                console.log(`📺 进度: ${i + 1}/${totalEpisodes} - 第 ${episode.number} 集`);

                const result = await this.fetchEpisodeVideoLinks(episode.link, episode.number);
                results.push(result);

                // 更新进度
                const progress = Math.round(((i + 1) / totalEpisodes) * 100);
                console.log(`✅ 第 ${episode.number} 集完成 (${progress}%)`);

                // 延迟避免请求过快
                if (i < totalEpisodes - 1) {
                    console.log(`⏳ 等待 ${this.delayBetweenRequests / 1000} 秒...`);
                    await this.delay(this.delayBetweenRequests);
                }
            }

            this.allEpisodesData = results;
            return results;
        }

        // 主提取函数
        async extract() {
            console.log('🚀 开始提取数据...');

            // 1. 提取页面剧集信息
            const pageEpisodes = this.extractEpisodesFromPage();

            // 2. 查找当前页面视频源
            const videoSources = this.findVideoSources();

            // 3. 尝试API获取
            const apiResults = await this.fetchEpisodesFromAPI();

            // 4. 获取所有剧集的视频链接
            console.log('🎬 开始获取所有剧集的视频链接...');
            const allEpisodesData = await this.fetchAllEpisodesVideoLinks();

            // 5. 整合结果
            const result = {
                seriesId: this.seriesId,
                title: document.title.replace(' - FlexTV', ''),
                pageUrl: window.location.href,
                extractTime: new Date().toISOString(),
                episodes: pageEpisodes,
                videoSources: videoSources,
                apiResults: apiResults,
                allEpisodesData: allEpisodesData,
                networkRequests: this.apiRequests,
                totalEpisodes: pageEpisodes.length,
                successfulExtractions: allEpisodesData.filter(ep => ep.success && ep.videoLinks.length > 0).length
            };

            console.log('✅ 提取完成！');
            console.table(pageEpisodes);

            // 显示视频链接统计
            const videoLinksCount = allEpisodesData.reduce((total, ep) => total + ep.videoLinks.length, 0);
            console.log(`🎥 总共提取到 ${videoLinksCount} 个视频链接`);

            return result;
        }

        // 导出数据
        exportData(data) {
            const jsonStr = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `flextv_all_episodes_${data.seriesId}_${timestamp}.json`;

            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.textContent = `📁 下载完整数据 ${filename}`;
            link.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                background: #28a745;
                color: white;
                padding: 12px 20px;
                text-decoration: none;
                border-radius: 6px;
                font-family: Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;

            link.onmouseover = () => {
                link.style.background = '#218838';
                link.style.transform = 'translateY(-2px)';
            };

            link.onmouseout = () => {
                link.style.background = '#28a745';
                link.style.transform = 'translateY(0)';
            };

            document.body.appendChild(link);

            // 同时创建一个简化版的视频链接文件
            this.exportVideoLinksOnly(data);

            // 3秒后自动下载完整数据
            setTimeout(() => {
                link.click();
                console.log('📁 完整数据已自动下载');
            }, 3000);
        }

        // 导出仅包含视频链接的简化数据
        exportVideoLinksOnly(data) {
            const videoLinksData = {
                seriesId: data.seriesId,
                title: data.title,
                extractTime: data.extractTime,
                totalEpisodes: data.totalEpisodes,
                successfulExtractions: data.successfulExtractions,
                episodes: data.allEpisodesData.map(ep => ({
                    episodeNumber: ep.episodeNumber,
                    episodeUrl: ep.episodeUrl,
                    videoLinks: ep.videoLinks,
                    success: ep.success,
                    error: ep.error
                })).filter(ep => ep.videoLinks && ep.videoLinks.length > 0)
            };

            const jsonStr = JSON.stringify(videoLinksData, null, 2);
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `flextv_video_links_${data.seriesId}_${timestamp}.json`;

            // 创建下载链接
            const linkVideoOnly = document.createElement('a');
            linkVideoOnly.href = url;
            linkVideoOnly.download = filename;
            linkVideoOnly.textContent = `🎥 下载视频链接 ${filename}`;
            linkVideoOnly.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 9999;
                background: #007bff;
                color: white;
                padding: 12px 20px;
                text-decoration: none;
                border-radius: 6px;
                font-family: Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;

            linkVideoOnly.onmouseover = () => {
                linkVideoOnly.style.background = '#0056b3';
                linkVideoOnly.style.transform = 'translateY(-2px)';
            };

            linkVideoOnly.onmouseout = () => {
                linkVideoOnly.style.background = '#007bff';
                linkVideoOnly.style.transform = 'translateY(0)';
            };

            document.body.appendChild(linkVideoOnly);

            // 5秒后自动下载视频链接文件
            setTimeout(() => {
                linkVideoOnly.click();
                console.log('🎥 视频链接文件已自动下载');
            }, 5000);
        }
    }

    // 执行提取
    const extractor = new FlexTVExtractor();
    extractor.extract().then(data => {
        console.log('🎉 最终结果:', data);
        extractor.exportData(data);

        // 将结果存储到全局变量，方便后续使用
        window.flextv_data = data;
        console.log('💾 数据已保存到 window.flextv_data');
    }).catch(error => {
        console.error('❌ 提取失败:', error);
    });

})();

console.log(`
🎬 FlexTV 全集视频提取器 - 增强版

✨ 新功能：
- 🚀 自动获取所有剧集的视频下载链接
- 📺 逐集访问页面提取真实视频源
- 🎥 支持 M3U8 和 MP4 格式
- 📊 实时进度显示和统计
- 💾 双文件导出（完整数据 + 纯视频链接）

使用方法：
1. 在指纹浏览器中打开任意 FlexTV 视频页面
2. 按 F12 打开开发者工具
3. 切换到 Console 标签
4. 粘贴此脚本并按回车
5. 等待自动提取所有剧集（可能需要几分钟）
6. 会自动下载两个JSON文件

提取内容：
✅ 所有剧集列表和链接
✅ 每集的真实视频下载地址
✅ M3U8 流媒体链接
✅ MP4 直链（如果有）
✅ API请求数据和网络记录
✅ 提取成功率统计

⚠️ 注意事项：
- 提取过程会自动访问每一集页面
- 请保持网络连接稳定
- 建议在登录状态下运行
- 提取时间取决于剧集数量

提示：提取完成后可通过 window.flextv_data 访问完整数据
`);
