// FlexTV 多集视频提取器 - 专门提取剧集列表和下载链接
// 在视频页面的浏览器控制台中运行

(function() {
    'use strict';
    
    console.log('🎭 FlexTV 多集视频提取器启动...');
    
    class FlexTVExtractor {
        constructor() {
            this.seriesId = this.extractSeriesId();
            this.episodes = [];
            this.videoSources = [];
            this.apiRequests = [];
            this.setupNetworkInterception();
        }
        
        // 从URL提取series_id
        extractSeriesId() {
            const urlMatch = window.location.pathname.match(/([^-]+)$/);
            return urlMatch ? urlMatch[1] : null;
        }
        
        // 设置网络请求拦截
        setupNetworkInterception() {
            const self = this;
            const originalFetch = window.fetch;
            
            window.fetch = function(...args) {
                const url = args[0];
                if (typeof url === 'string') {
                    self.logApiRequest('fetch', url);
                }
                return originalFetch.apply(this, args);
            };
        }
        
        // 记录API请求
        logApiRequest(type, url) {
            if (url.includes('api-quick.flextv.cc') || 
                url.includes('video') || 
                url.includes('m3u8') || 
                url.includes('mp4')) {
                
                this.apiRequests.push({
                    type,
                    url,
                    timestamp: Date.now()
                });
                console.log(`🌐 [${type.toUpperCase()}] ${url}`);
            }
        }
        
        // 提取页面中的剧集信息
        extractEpisodesFromPage() {
            const episodes = [];
            
            // 查找剧集编号按钮或链接
            const episodeSelectors = [
                '[class*="episode"]',
                '[class*="section"]', 
                '.episode-item',
                '.section-item',
                'button[class*="episode"]',
                'a[href*="episode"]',
                '.drama_list .item'
            ];
            
            episodeSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach((element, index) => {
                    const episode = this.parseEpisodeElement(element, index);
                    if (episode) {
                        episodes.push(episode);
                    }
                });
            });
            
            // 去重
            const uniqueEpisodes = episodes.filter((episode, index, self) => 
                index === self.findIndex(e => e.number === episode.number || e.link === episode.link)
            );
            
            this.episodes = uniqueEpisodes;
            console.log(`📺 找到 ${this.episodes.length} 集`);
            return this.episodes;
        }
        
        // 解析单个剧集元素
        parseEpisodeElement(element, index) {
            const episode = {
                number: index + 1,
                title: '',
                link: '',
                thumbnail: '',
                isActive: false
            };
            
            // 提取标题
            const titleSelectors = ['.title', 'h3', '.episode-title', '.section-title'];
            for (const selector of titleSelectors) {
                const titleEl = element.querySelector(selector);
                if (titleEl) {
                    episode.title = titleEl.textContent.trim();
                    break;
                }
            }
            
            // 提取链接
            const linkEl = element.querySelector('a') || (element.tagName === 'A' ? element : null);
            if (linkEl) {
                episode.link = linkEl.href;
            }
            
            // 提取缩略图
            const imgEl = element.querySelector('img');
            if (imgEl) {
                episode.thumbnail = imgEl.src;
            }
            
            // 检查是否为当前激活的剧集
            episode.isActive = element.classList.contains('active') || 
                              element.classList.contains('current') ||
                              element.classList.contains('selected');
            
            // 尝试从文本中提取剧集编号
            const text = element.textContent.trim();
            const numberMatch = text.match(/(\d+)/);
            if (numberMatch) {
                episode.number = parseInt(numberMatch[1]);
            }
            
            return episode.title || episode.link ? episode : null;
        }
        
        // 尝试通过API获取剧集数据
        async fetchEpisodesFromAPI() {
            if (!this.seriesId) {
                console.log('❌ 无法获取 series_id');
                return [];
            }
            
            const apiEndpoints = [
                `https://api-quick.flextv.cc/webGetSeriesSectionFullList?series_id=${this.seriesId}&series_no=1`,
                `https://api-quick.flextv.cc/webGetSeriesSectionFullList?series_id=${this.seriesId}&series_no=-1`,
                `https://api-quick.flextv.cc/webGetSeriesLang?series_id=${this.seriesId}`
            ];
            
            const results = [];
            
            for (const url of apiEndpoints) {
                try {
                    console.log(`🔍 尝试API: ${url}`);
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    results.push({
                        url,
                        data,
                        success: data.code === 0
                    });
                    
                    console.log(`📊 API响应:`, data);
                    
                    // 如果数据被加密，尝试解密
                    if (data.data && typeof data.data === 'string' && data.data.length > 100) {
                        console.log('🔐 检测到加密数据，长度:', data.data.length);
                    }
                    
                } catch (error) {
                    console.log(`❌ API请求失败: ${error.message}`);
                    results.push({
                        url,
                        error: error.message,
                        success: false
                    });
                }
            }
            
            return results;
        }
        
        // 查找视频源
        findVideoSources() {
            const sources = [];
            
            // 查找video标签
            const videos = document.querySelectorAll('video');
            videos.forEach((video, index) => {
                const videoData = {
                    index,
                    element: 'video',
                    src: video.src || video.currentSrc,
                    sources: []
                };
                
                const sourceTags = video.querySelectorAll('source');
                sourceTags.forEach(source => {
                    videoData.sources.push({
                        src: source.src,
                        type: source.type
                    });
                });
                
                if (videoData.src || videoData.sources.length > 0) {
                    sources.push(videoData);
                }
            });
            
            // 查找iframe中的视频
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach((iframe, index) => {
                if (iframe.src && (iframe.src.includes('video') || iframe.src.includes('player'))) {
                    sources.push({
                        index,
                        element: 'iframe',
                        src: iframe.src,
                        sources: []
                    });
                }
            });
            
            this.videoSources = sources;
            console.log(`🎥 找到 ${sources.length} 个视频源`);
            return sources;
        }
        
        // 主提取函数
        async extract() {
            console.log('🚀 开始提取数据...');
            
            // 1. 提取页面剧集信息
            const pageEpisodes = this.extractEpisodesFromPage();
            
            // 2. 查找视频源
            const videoSources = this.findVideoSources();
            
            // 3. 尝试API获取
            const apiResults = await this.fetchEpisodesFromAPI();
            
            // 4. 整合结果
            const result = {
                seriesId: this.seriesId,
                title: document.title.replace(' - FlexTV', ''),
                pageUrl: window.location.href,
                extractTime: new Date().toISOString(),
                episodes: pageEpisodes,
                videoSources: videoSources,
                apiResults: apiResults,
                networkRequests: this.apiRequests,
                totalEpisodes: pageEpisodes.length
            };
            
            console.log('✅ 提取完成！');
            console.table(pageEpisodes);
            
            return result;
        }
        
        // 导出数据
        exportData(data) {
            const jsonStr = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `flextv_episodes_${this.seriesId || 'unknown'}.json`;
            link.textContent = '📥 下载剧集数据';
            link.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                background: #28a745;
                color: white;
                padding: 12px 20px;
                text-decoration: none;
                border-radius: 6px;
                font-family: Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
            `;
            
            link.onmouseover = () => {
                link.style.background = '#218838';
                link.style.transform = 'translateY(-2px)';
            };
            
            link.onmouseout = () => {
                link.style.background = '#28a745';
                link.style.transform = 'translateY(0)';
            };
            
            document.body.appendChild(link);
            
            // 3秒后自动下载
            setTimeout(() => {
                link.click();
                console.log('📁 数据已自动下载');
            }, 3000);
        }
    }
    
    // 执行提取
    const extractor = new FlexTVExtractor();
    extractor.extract().then(data => {
        console.log('🎉 最终结果:', data);
        extractor.exportData(data);
        
        // 将结果存储到全局变量，方便后续使用
        window.flextv_data = data;
        console.log('💾 数据已保存到 window.flextv_data');
    }).catch(error => {
        console.error('❌ 提取失败:', error);
    });
    
})();

console.log(`
🎬 FlexTV 多集视频提取器

使用方法：
1. 在指纹浏览器中打开任意 FlexTV 视频页面
2. 按 F12 打开开发者工具
3. 切换到 Console 标签
4. 粘贴此脚本并按回车
5. 等待提取完成，会自动下载JSON文件

提取内容：
✅ 所有剧集列表
✅ 剧集标题和链接  
✅ 视频源地址
✅ API请求数据
✅ 网络请求记录

提示：提取完成后可通过 window.flextv_data 访问数据
`);
