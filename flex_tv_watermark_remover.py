#!/usr/bin/env python3
"""
专门去除右上角Flex TV水印的FFmpeg脚本
"""

import subprocess
import os
import json
from datetime import datetime

def get_video_info(video_path):
    """获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video':
                    return {
                        'width': int(stream.get('width', 0)),
                        'height': int(stream.get('height', 0)),
                        'fps': eval(stream.get('r_frame_rate', '25/1')),
                        'duration': float(info['format'].get('duration', 0))
                    }
        return None
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return None

def remove_flex_tv_delogo(input_path, output_path, width, height):
    """
    使用delogo滤镜去除右上角Flex TV水印
    """
    # 右上角水印位置计算 - 进一步扩大区域以确保完全覆盖所有水印元素
    # 大幅扩大水印区域以确保完全覆盖右上角的所有标志
    watermark_width = int(width * 0.22)  # 从18%增加到22%
    watermark_height = int(height * 0.12)  # 从10%增加到12%
    watermark_x = width - watermark_width - 5  # 距离右边减少到5像素
    watermark_y = 5  # 距离顶部减少到5像素
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={watermark_x}:y={watermark_y}:w={watermark_width}:h={watermark_height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 去除右上角Flex TV水印 (delogo方法)")
    print(f"   水印位置: ({watermark_x}, {watermark_y})")
    print(f"   水印大小: {watermark_width}x{watermark_height}")
    
    return run_ffmpeg_with_progress(cmd, "Delogo去除Flex TV水印")

def remove_subtitles_delogo(input_path, output_path, width, height):
    """
    使用delogo去除底部字幕区域
    """
    # 字幕通常在底部10-15%的区域
    subtitle_height = int(height * 0.15)
    subtitle_y = height - subtitle_height

    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x=0:y={subtitle_y}:w={width}:h={subtitle_height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]

    print(f"📝 去除底部字幕区域 (delogo方法)")
    print(f"   字幕区域: (0, {subtitle_y}) - {width}x{subtitle_height}")

    return run_ffmpeg_with_progress(cmd, "Delogo去除字幕")

def remove_flex_tv_blur(input_path, output_path, width, height):
    """
    使用模糊滤镜去除右上角Flex TV水印
    """
    watermark_width = int(width * 0.18)
    watermark_height = int(height * 0.10)
    watermark_x = width - watermark_width - 10
    watermark_y = 10
    
    # 创建一个遮罩，只对水印区域进行模糊
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'boxblur=15:1:cr=0:ar=0:enable=\'between(x,{watermark_x},{watermark_x + watermark_width})*between(y,{watermark_y},{watermark_y + watermark_height})\'',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🌫️  模糊去除右上角Flex TV水印")
    print(f"   模糊区域: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")
    
    return run_ffmpeg_with_progress(cmd, "模糊去除Flex TV水印")

def remove_flex_tv_cover(input_path, output_path, width, height):
    """
    使用纯色覆盖去除右上角Flex TV水印
    """
    watermark_width = int(width * 0.15)
    watermark_height = int(height * 0.08)
    watermark_x = width - watermark_width - 20
    watermark_y = 20
    
    # 使用drawbox在水印位置绘制一个与背景相近的颜色块
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'drawbox=x={watermark_x}:y={watermark_y}:w={watermark_width}:h={watermark_height}:color=black@0.8:t=fill',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎨 覆盖去除右上角Flex TV水印")
    print(f"   覆盖区域: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")
    
    return run_ffmpeg_with_progress(cmd, "覆盖去除Flex TV水印")

def remove_flex_tv_advanced_delogo(input_path, output_path, width, height):
    """
    高级delogo，处理多个可能的水印位置
    """
    # 主要水印位置（右上角）- 扩大覆盖范围
    main_x = width - int(width * 0.18) - 10
    main_y = 10
    main_w = int(width * 0.18)
    main_h = int(height * 0.10)

    # 可能的小logo位置（右上角更小的区域）- 也相应调整
    small_x = width - int(width * 0.10) - 5
    small_y = 5
    small_w = int(width * 0.10)
    small_h = int(height * 0.05)
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={main_x}:y={main_y}:w={main_w}:h={main_h},delogo=x={small_x}:y={small_y}:w={small_w}:h={small_h}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 高级delogo去除Flex TV水印")
    print(f"   主要区域: ({main_x}, {main_y}) - {main_w}x{main_h}")
    print(f"   次要区域: ({small_x}, {small_y}) - {small_w}x{small_h}")
    
    return run_ffmpeg_with_progress(cmd, "高级delogo去除Flex TV水印")

def remove_watermark_median_filter(input_path, output_path, width, height):
    """
    使用中值滤波去除右上角水印
    """
    watermark_width = int(width * 0.15)
    watermark_height = int(height * 0.08)
    watermark_x = width - watermark_width - 20
    watermark_y = 20

    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'median=radius=5:percentile=0.5:enable=\'between(x,{watermark_x},{watermark_x + watermark_width})*between(y,{watermark_y},{watermark_y + watermark_height})\'',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]

    print(f"🔍 中值滤波去除右上角水印")
    print(f"   处理区域: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")

    return run_ffmpeg_with_progress(cmd, "中值滤波去除水印")

def remove_watermark_edge_fill(input_path, output_path, width, height):
    """
    使用边缘填充去除右上角水印
    """
    watermark_width = int(width * 0.15)
    watermark_height = int(height * 0.08)
    watermark_x = width - watermark_width - 20
    watermark_y = 20

    # 使用fillborders滤镜进行边缘填充
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'crop={watermark_x}:{watermark_y}:{watermark_width}:{watermark_height},fillborders=left=5:right=5:top=5:bottom=5:mode=mirror[filled];[0:v][filled]overlay={watermark_x}:{watermark_y}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]

    print(f"🪞 边缘填充去除右上角水印")
    print(f"   填充区域: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")

    return run_ffmpeg_with_progress(cmd, "边缘填充去除水印")

def remove_watermark_temporal_repair(input_path, output_path, width, height):
    """
    使用时间域修复去除水印（利用前后帧信息）
    """
    watermark_width = int(width * 0.18)
    watermark_height = int(height * 0.10)
    watermark_x = width - watermark_width - 10
    watermark_y = 10

    # 使用bwdif去隔行 + atadenoise降噪来利用时间信息
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'atadenoise=s=9:p=7:a=0.25,delogo=x={watermark_x}:y={watermark_y}:w={watermark_width}:h={watermark_height}:show=0',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]

    print(f"⏰ 时间域修复去除右上角水印")
    print(f"   修复区域: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")

    return run_ffmpeg_with_progress(cmd, "时间域修复去除水印")

def remove_watermark_inpaint(input_path, output_path, width, height):
    """
    使用修复算法去除水印（类似PS的内容感知填充）
    """
    watermark_width = int(width * 0.15)
    watermark_height = int(height * 0.08)
    watermark_x = width - watermark_width - 20
    watermark_y = 20

    # 使用removegrain + repair滤镜组合进行修复
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'removegrain=m=4,delogo=x={watermark_x}:y={watermark_y}:w={watermark_width}:h={watermark_height}:show=0',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]

    print(f"🎨 智能修复去除右上角水印")
    print(f"   修复区域: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")

    return run_ffmpeg_with_progress(cmd, "智能修复去除水印")

def run_ffmpeg_with_progress(cmd, operation_name):
    """执行FFmpeg命令并显示进度"""
    print(f"🚀 开始: {operation_name}")
    print(f"📝 命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # 显示包含时间信息的行
                if 'time=' in output and 'speed=' in output:
                    # 提取时间和速度信息
                    parts = output.strip().split()
                    time_info = next((p for p in parts if p.startswith('time=')), '')
                    speed_info = next((p for p in parts if p.startswith('speed=')), '')
                    if time_info and speed_info:
                        print(f"\r  ⏱️  {time_info} {speed_info}", end='', flush=True)
        
        print()  # 换行
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"✅ {operation_name} 完成!")
            return True
        else:
            print(f"❌ {operation_name} 失败!")
            return False
            
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🎬 Flex TV水印专业去除工具 (FFmpeg版)")
    print("=" * 50)
    
    input_video = "/Users/<USER>/Work/Script/temp3/new_downloaded_video.mp4"
    
    if not os.path.exists(input_video):
        print(f"❌ 输入视频不存在: {input_video}")
        return
    
    # 获取视频信息
    print(f"📋 分析视频信息...")
    video_info = get_video_info(input_video)
    
    if not video_info:
        print("❌ 无法获取视频信息")
        return
    
    width, height = video_info['width'], video_info['height']
    print(f"✅ 视频信息: {width}x{height}, {video_info['fps']:.2f}fps, {video_info['duration']:.1f}s")
    
    # 定义处理方法
    methods = [
        {
            'name': 'delogo',
            'description': 'Delogo智能修复',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_delogo.mp4',
            'function': lambda: remove_flex_tv_delogo(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_delogo.mp4', width, height)
        },
        {
            'name': 'subtitle_delogo',
            'description': '字幕Delogo去除',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_subtitle_removed.mp4',
            'function': lambda: remove_subtitles_delogo(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_subtitle_removed.mp4', width, height)
        },
        {
            'name': 'blur',
            'description': '区域模糊',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_blur.mp4',
            'function': lambda: remove_flex_tv_blur(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_blur.mp4', width, height)
        },
        {
            'name': 'advanced',
            'description': '高级Delogo',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_advanced.mp4',
            'function': lambda: remove_flex_tv_advanced_delogo(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_advanced.mp4', width, height)
        },
        {
            'name': 'median_filter',
            'description': '中值滤波修复',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_median.mp4',
            'function': lambda: remove_watermark_median_filter(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_median.mp4', width, height)
        },
        {
            'name': 'edge_fill',
            'description': '边缘填充修复',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_edge_fill.mp4',
            'function': lambda: remove_watermark_edge_fill(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_edge_fill.mp4', width, height)
        },
        {
            'name': 'temporal_repair',
            'description': '时间域修复',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_temporal.mp4',
            'function': lambda: remove_watermark_temporal_repair(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_temporal.mp4', width, height)
        },
        {
            'name': 'inpaint',
            'description': '智能修复算法',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_inpaint.mp4',
            'function': lambda: remove_watermark_inpaint(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_inpaint.mp4', width, height)
        }
    ]
    
    # 执行所有方法
    results = []
    for i, method in enumerate(methods, 1):
        print(f"\n🔧 [{i}/{len(methods)}] {method['description']}")
        start_time = datetime.now()
        
        success = method['function']()
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        results.append({
            'method': method['name'],
            'description': method['description'],
            'output': method['output'],
            'success': success,
            'processing_time': processing_time
        })
    
    # 显示结果
    print(f"\n📊 处理结果:")
    print("-" * 60)
    
    successful_files = []
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['description']:<15}: {status} ({result['processing_time']:.1f}s)")
        
        if result['success'] and os.path.exists(result['output']):
            size_mb = os.path.getsize(result['output']) / (1024 * 1024)
            filename = os.path.basename(result['output'])
            print(f"                    📁 {filename} ({size_mb:.2f} MB)")
            successful_files.append(result['output'])
    
    # 推荐
    print(f"\n🏆 推荐:")
    print("-" * 60)
    if successful_files:
        print("✅ 建议按以下顺序测试效果:")
        print("\n🎯 水印去除方法 (推荐顺序):")
        print("   1. flex_tv_removed_delogo.mp4 (基础Delogo，通常效果最好)")
        print("   2. flex_tv_removed_inpaint.mp4 (智能修复算法)")
        print("   3. flex_tv_removed_temporal.mp4 (时间域修复，利用前后帧)")
        print("   4. flex_tv_removed_advanced.mp4 (多区域处理)")
        print("   5. flex_tv_removed_median.mp4 (中值滤波)")
        print("   6. flex_tv_removed_edge_fill.mp4 (边缘填充)")
        print("   7. flex_tv_removed_blur.mp4 (模糊方法，保守处理)")

        print("\n📝 字幕处理:")
        print("   8. flex_tv_subtitle_removed.mp4 (字幕去除)")

        print(f"\n💡 使用建议:")
        print("• 用视频播放器逐一查看效果")
        print("• Delogo和智能修复方法通常效果最自然")
        print("• 时间域修复适合静态水印，效果更平滑")
        print("• 所有方法都保持视频完整性，不会裁剪内容")
        print("• 建议优先尝试前3种方法")
    else:
        print("❌ 所有方法都失败了，请检查FFmpeg安装和视频文件")
    
    print(f"\n🎉 Flex TV水印去除完成!")

if __name__ == "__main__":
    main()
