# 🎓 浏览器指纹学术研究项目

Browser Fingerprint Academic Research Project

## 📋 项目概述

本项目是一个专为学术研究设计的浏览器指纹收集和视频API分析工具集，旨在帮助研究人员理解现代Web技术、隐私保护机制和媒体平台的技术架构。

**⚠️ 重要声明：本项目仅用于学术研究和教育目的，严禁用于恶意跟踪、隐私侵犯或其他非法用途。**

## 🎯 研究功能

### 🔍 浏览器指纹收集
- **基础信息**：User-Agent、语言、平台等
- **硬件特征**：屏幕分辨率、CPU核心数、内存信息
- **Canvas指纹**：图形渲染差异分析
- **WebGL指纹**：GPU和驱动程序特征
- **音频指纹**：音频处理硬件特性
- **字体检测**：系统安装字体识别
- **存储检测**：本地存储能力分析

### 🌐 网络请求监控
- **实时拦截**：Fetch API和XMLHttpRequest
- **请求分析**：URL模式、参数、头部信息
- **响应解析**：JSON数据、文本内容分析
- **性能监控**：资源加载时间和大小

### 🎥 视频API专项分析
- **API识别**：自动识别视频相关接口
- **URL提取**：从响应中提取媒体文件链接
- **剧集分析**：智能识别剧集信息和元数据
- **流媒体检测**：M3U8、MPD等流媒体格式

## 📁 项目结构

```
浏览器指纹学术研究项目/
├── browser_fingerprint_collector.js    # 🔧 核心指纹收集工具
├── fingerprint_analyzer.py             # 📊 Python数据分析工具
├── 启动项目.html                       # 🚀 Web启动页面
├── start_project.py                     # 🐍 Python启动脚本
├── 启动项目.bat                        # 🪟 Windows批处理启动
├── 启动项目.sh                         # 🐧 Linux/Mac Shell启动
├── 学术研究工具使用指南.md              # 📚 详细使用文档
├── 浏览器指纹技术学习指南.md            # 📖 技术学习资料
└── README.md                           # 📋 项目说明文件
```

## 🚀 快速启动

### 方法一：一键启动（推荐）

#### Windows用户
```batch
# 双击运行
启动项目.bat
```

#### Linux/Mac用户
```bash
# 给脚本执行权限
chmod +x 启动项目.sh

# 运行启动脚本
./启动项目.sh
```

### 方法二：Python启动
```bash
# 确保Python 3.6+已安装
python start_project.py
# 或
python3 start_project.py
```

### 方法三：手动启动
```bash
# 启动HTTP服务器
python -m http.server 8080
# 或
python3 -m http.server 8080

# 然后在浏览器中访问
# http://localhost:8080/启动项目.html
```

## 🛠️ 使用方法

### 第一步：启动项目
1. 运行启动脚本
2. 等待浏览器自动打开
3. 查看项目启动页面

### 第二步：开始研究
1. **测试环境**：点击"加载测试页面"创建测试环境
2. **目标网站**：在要研究的网站打开开发者工具(F12)
3. **运行工具**：复制粘贴研究工具代码到控制台
4. **观察输出**：查看实时数据收集和分析结果

### 第三步：数据分析
1. **下载数据**：点击自动生成的下载链接
2. **Python分析**：使用`fingerprint_analyzer.py`处理数据
3. **生成报告**：获得详细的分析报告和可视化图表

## 📊 数据格式

### 指纹数据结构
```json
{
  "timestamp": "2025-01-12T10:30:00.000Z",
  "hash": "unique_fingerprint_hash",
  "basic": {
    "userAgent": "Mozilla/5.0...",
    "platform": "Win32",
    "language": "zh-CN"
  },
  "canvas": "data:image/png;base64...",
  "webgl": {
    "vendor": "Google Inc.",
    "renderer": "ANGLE (NVIDIA...)"
  },
  "networkRequests": [...],
  "videoSources": [...],
  "apiEndpoints": [...]
}
```

### API分析报告
```json
{
  "summary": {
    "totalAPICalls": 45,
    "videoAPICalls": 12,
    "videoURLsFound": 8,
    "episodesFound": 30
  },
  "videoUrls": [...],
  "episodeData": [...]
}
```

## 🔬 研究应用

### 学术研究领域
- **隐私保护研究**：分析指纹技术的唯一性和有效性
- **Web安全研究**：理解现代网站的安全防护机制
- **媒体技术分析**：研究视频流媒体和DRM技术
- **网络协议研究**：分析HTTP/HTTPS请求模式

### 教育用途
- **技术教学**：帮助学生理解Web技术原理
- **隐私教育**：提高对网络隐私的认识
- **安全培训**：培养网络安全意识
- **研究方法**：学习数据收集和分析方法

## 📈 系统要求

### 基础要求
- **Python**: 3.6或更高版本
- **浏览器**: Chrome、Firefox、Safari、Edge等现代浏览器
- **操作系统**: Windows、macOS、Linux

### 可选依赖
```bash
# 用于数据可视化（可选）
pip install matplotlib pandas numpy
```

## ⚖️ 伦理和法律

### 研究伦理
- ✅ **知情同意**：在可能的情况下告知用户数据收集
- ✅ **最小化原则**：只收集研究必需的数据
- ✅ **数据保护**：安全存储和处理收集的数据
- ✅ **匿名化**：移除或混淆个人身份信息

### 法律合规
- ✅ **遵守当地法律**：了解并遵守数据保护法规
- ✅ **服务条款**：尊重网站的使用条款
- ✅ **版权保护**：不侵犯内容版权
- ✅ **学术诚信**：正确引用和归属研究成果

## 🤝 贡献指南

### 如何贡献
1. **Fork项目**：创建项目的分支
2. **提交改进**：修复bug或添加新功能
3. **测试验证**：确保代码质量和功能正确性
4. **提交PR**：提交Pull Request供审核

### 贡献类型
- 🐛 **Bug修复**：修复发现的问题
- ✨ **新功能**：添加有用的新特性
- 📚 **文档改进**：完善使用说明和技术文档
- 🎨 **界面优化**：改进用户体验和界面设计

## 📞 技术支持

### 常见问题
1. **启动失败**：检查Python版本和文件完整性
2. **浏览器兼容性**：使用最新版本的现代浏览器
3. **数据收集不完整**：确保页面完全加载后运行工具
4. **权限问题**：检查浏览器安全设置和CORS策略

### 获取帮助
- 📖 **查看文档**：阅读详细的使用指南
- 🔍 **检查日志**：查看控制台错误信息
- 🧪 **使用测试页面**：在测试环境中验证功能
- 💬 **社区讨论**：参与学术社区交流

## 📄 许可证

本项目采用学术研究许可证，仅允许用于：
- 🎓 学术研究和教育目的
- 📚 技术学习和能力提升
- 🔬 隐私保护技术研究
- 📊 数据分析方法学习

**严禁用于**：
- ❌ 恶意跟踪或隐私侵犯
- ❌ 商业用途或盈利活动
- ❌ 违法违规行为
- ❌ 侵犯他人权益

## 🙏 致谢

感谢所有为隐私保护和Web安全研究做出贡献的研究者和开发者。本项目的目标是推动技术进步，提高隐私保护意识，为构建更安全的网络环境贡献力量。

---

**免责声明**：本项目仅用于学术研究和教育目的。使用者应当遵守相关法律法规、道德准则和学术诚信原则。项目开发者不对任何不当使用承担责任。
