#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlexTV 下载器启动脚本 - 简化版本
"""

import os
import sys
import json
import time
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    required_packages = {
        'selenium': 'selenium',
        'requests': 'requests',
        'ffmpeg-python': 'ffmpeg',
        'webdriver-manager': 'webdriver_manager'
    }

    missing_packages = []
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    print("✅ 所有依赖包已安装")
    return True

def check_ffmpeg():
    """检查FFmpeg"""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到FFmpeg，请先安装FFmpeg")
        print("下载地址: https://ffmpeg.org/download.html")
        return False

def create_simple_config():
    """创建简化配置"""
    config = {
        "video_url": "https://www.flextv.cc/video/The_Secret_Recipe_to_Snatch_a_Billionaire-J9zD5pbn1x",
        "download_dir": "downloads",
        "proxy": {
            "enabled": False,
            "host": "127.0.0.1",
            "port": "7890"
        },
        "browser": {
            "headless": False,
            "profile_path": ""  # RoxyChrome配置路径
        }
    }
    
    with open('simple_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    return config

def run_simple_extractor():
    """运行简化版提取器"""
    print("🚀 FlexTV 简化版下载器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 检查FFmpeg（可选）
    has_ffmpeg = check_ffmpeg()
    if not has_ffmpeg:
        print("⚠️ 没有FFmpeg，将跳过M3U8下载")
    
    # 加载或创建配置
    if os.path.exists('simple_config.json'):
        with open('simple_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        config = create_simple_config()
        print("✅ 已创建配置文件: simple_config.json")
    
    # 导入Selenium
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 设置浏览器
    chrome_options = Options()
    
    if config['browser']['headless']:
        chrome_options.add_argument("--headless")
    
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    
    # 代理设置
    if config['proxy']['enabled']:
        proxy_string = f"{config['proxy']['host']}:{config['proxy']['port']}"
        chrome_options.add_argument(f"--proxy-server={proxy_string}")
    
    # RoxyChrome配置
    if config['browser']['profile_path']:
        chrome_options.add_argument(f"--user-data-dir={config['browser']['profile_path']}")
    
    try:
        # 启动浏览器
        print("🌐 启动浏览器...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # 访问页面
        video_url = config['video_url']
        print(f"📺 访问视频页面: {video_url}")
        driver.get(video_url)
        time.sleep(5)
        
        # 读取并注入提取脚本
        script_files = [
            'flextv_advanced_extractor.js',
            'flextv_episodes_extractor.js',
            'flextv_video_extractor.js'
        ]
        
        script_injected = False
        for script_file in script_files:
            if os.path.exists(script_file):
                print(f"📜 注入脚本: {script_file}")
                with open(script_file, 'r', encoding='utf-8') as f:
                    script_content = f.read()
                
                try:
                    driver.execute_script(script_content)
                    script_injected = True
                    break
                except Exception as e:
                    print(f"⚠️ 脚本注入失败: {e}")
        
        if not script_injected:
            print("❌ 没有找到可用的提取脚本")
            return False
        
        # 等待提取完成
        print("⏳ 等待视频信息提取完成...")
        max_wait = 300  # 5分钟
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                # 检查不同的全局变量
                for var_name in ['flextv_advanced_data', 'flextv_data', 'window.flextv_data']:
                    result = driver.execute_script(f"return {var_name};")
                    if result:
                        print("✅ 提取完成！")
                        
                        # 保存数据
                        os.makedirs(config['download_dir'], exist_ok=True)
                        timestamp = time.strftime("%Y%m%d_%H%M%S")
                        data_file = os.path.join(config['download_dir'], f"flextv_data_{timestamp}.json")
                        
                        with open(data_file, 'w', encoding='utf-8') as f:
                            json.dump(result, f, ensure_ascii=False, indent=2)
                        
                        print(f"💾 数据已保存: {data_file}")
                        
                        # 显示统计信息
                        total_episodes = result.get('totalEpisodes', 0)
                        successful = result.get('successfulExtractions', 0)
                        print(f"📊 统计: {successful}/{total_episodes} 集提取成功")
                        
                        # 提取视频链接
                        video_links = []
                        if 'videoData' in result:
                            for episode in result['videoData']:
                                if episode.get('success') and episode.get('videoLinks'):
                                    for link in episode['videoLinks']:
                                        video_links.append({
                                            'episode': episode['episodeNumber'],
                                            'url': link['url'],
                                            'type': link['type']
                                        })
                        elif 'allEpisodesData' in result:
                            for episode in result['allEpisodesData']:
                                if episode.get('success') and episode.get('videoLinks'):
                                    for link in episode['videoLinks']:
                                        video_links.append({
                                            'episode': episode['episodeNumber'],
                                            'url': link['url'],
                                            'type': link['type']
                                        })
                        
                        if video_links:
                            print(f"🎥 找到 {len(video_links)} 个视频链接")
                            
                            # 保存视频链接
                            links_file = os.path.join(config['download_dir'], f"video_links_{timestamp}.json")
                            with open(links_file, 'w', encoding='utf-8') as f:
                                json.dump(video_links, f, ensure_ascii=False, indent=2)
                            print(f"🔗 视频链接已保存: {links_file}")
                            
                            # 询问是否下载
                            if has_ffmpeg:
                                download_choice = input("\n是否开始下载视频？(y/n): ").lower()
                                if download_choice == 'y':
                                    download_videos(video_links, config['download_dir'])
                            else:
                                print("⚠️ 由于缺少FFmpeg，无法自动下载视频")
                                print("💡 您可以使用保存的链接手动下载")
                        
                        return True
                
                time.sleep(3)
                
            except Exception as e:
                print(f"⚠️ 检查提取状态时出错: {e}")
                time.sleep(5)
        
        print("❌ 提取超时")
        return False
        
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return False
    
    finally:
        if 'driver' in locals():
            driver.quit()
            print("🔚 浏览器已关闭")

def download_videos(video_links, download_dir):
    """简单的视频下载"""
    import requests
    
    print(f"📥 开始下载 {len(video_links)} 个视频...")
    
    for i, video in enumerate(video_links, 1):
        episode = video['episode']
        url = video['url']
        video_type = video['type']
        
        print(f"📺 下载第 {episode} 集 ({i}/{len(video_links)})")
        
        try:
            if video_type == 'hls' or url.endswith('.m3u8'):
                # 使用ffmpeg下载M3U8
                output_file = os.path.join(download_dir, f"Episode_{episode:02d}.mp4")
                cmd = [
                    'ffmpeg', '-i', url, '-c', 'copy', '-y', output_file
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ 第 {episode} 集下载成功")
                else:
                    print(f"❌ 第 {episode} 集下载失败: {result.stderr}")
            
            else:
                # 直接下载MP4
                response = requests.get(url, stream=True)
                if response.status_code == 200:
                    output_file = os.path.join(download_dir, f"Episode_{episode:02d}.mp4")
                    with open(output_file, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                    print(f"✅ 第 {episode} 集下载成功")
                else:
                    print(f"❌ 第 {episode} 集下载失败: HTTP {response.status_code}")
        
        except Exception as e:
            print(f"❌ 第 {episode} 集下载出错: {e}")
        
        time.sleep(1)  # 避免请求过快

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == '--help':
            print("""
FlexTV 下载器使用说明:

1. 基本使用:
   python run_downloader.py

2. 配置文件:
   编辑 simple_config.json 修改设置

3. 依赖安装:
   pip install -r requirements.txt

4. FFmpeg安装:
   从 https://ffmpeg.org/download.html 下载安装
            """)
            return
    
    success = run_simple_extractor()
    if success:
        print("\n🎉 任务完成！")
    else:
        print("\n❌ 任务失败！")
        print("💡 请检查网络连接、代理设置和依赖安装")

if __name__ == "__main__":
    main()
