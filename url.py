import requests
import os
from urllib.parse import urlparse
import time

cookies = {
    'language': 'en',
    '_gid': 'GA1.2.**********.**********',
    '__gads': 'ID=edc7ae4b62506779:T=**********:RT=**********:S=ALNI_MZHIJAyuQ10wjEpmrBDAEjlS-Mm0g',
    '__gpi': 'UID=0000125b55d9c257:T=**********:RT=**********:S=ALNI_MaHZ0I6OoEfpEfWCLQnBw05txiNGQ',
    '__eoi': 'ID=f71b8a19c2b2ca3d:T=**********:RT=**********:S=AA-AfjZKHIfNVSxtwmHd2Zog4mlk',
    'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************.jtNlQi-rNkMhyJ1gkGipLpfo-zq2DM0B8Gv8mZiXyM0',
    'FCNEC': '%5B%5B%22AKsRol8HBJ2XWgeEnL6EfQ46pzHfnu3tZ1H5SLBZARgq7VT3n9DOaUpfSV5ZCfia1vKrnOBmnXT2hUZaRy-KCAF6Qf2op7efEOTlvCb0kru4U6ruVGICJDGW3wolXJrGECo3iDZiO_xKsMe9NcSKOlshWklNO3Uw_w%3D%3D%22%5D%5D',
    '_ga_J53YKEV8JL': 'GS2.1.s**********$o1$g1$t1754884562$j60$l0$h0',
    '_ga': 'GA1.2.*********.**********',
    '_gat_gtag_UA_239990267_1': '1',
}

headers = {
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9',
    'Connection': 'keep-alive',
    'Range': 'bytes=0-',
    'Sec-Fetch-Dest': 'video',
    'Sec-Fetch-Mode': 'no-cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    # 'Cookie': 'language=en; _gid=GA1.2.**********.**********; __gads=ID=edc7ae4b62506779:T=**********:RT=**********:S=ALNI_MZHIJAyuQ10wjEpmrBDAEjlS-Mm0g; __gpi=UID=0000125b55d9c257:T=**********:RT=**********:S=ALNI_MaHZ0I6OoEfpEfWCLQnBw05txiNGQ; __eoi=ID=f71b8a19c2b2ca3d:T=**********:RT=**********:S=AA-AfjZKHIfNVSxtwmHd2Zog4mlk; token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************.jtNlQi-rNkMhyJ1gkGipLpfo-zq2DM0B8Gv8mZiXyM0; FCNEC=%5B%5B%22AKsRol8HBJ2XWgeEnL6EfQ46pzHfnu3tZ1H5SLBZARgq7VT3n9DOaUpfSV5ZCfia1vKrnOBmnXT2hUZaRy-KCAF6Qf2op7efEOTlvCb0kru4U6ruVGICJDGW3wolXJrGECo3iDZiO_xKsMe9NcSKOlshWklNO3Uw_w%3D%3D%22%5D%5D; _ga_J53YKEV8JL=GS2.1.s**********$o1$g1$t1754884562$j60$l0$h0; _ga=GA1.2.*********.**********; _gat_gtag_UA_239990267_1=1',
}

params = {
    'auth_key': '**********-689969d3d27cc-0-de13b8ff54c7c0fdbebba1aa4d3e6f63',
}

def download_video(url, filename=None):
    """
    下载视频文件到本地
    """
    if filename is None:
        # 从URL中提取文件名
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        if not filename or not filename.endswith('.mp4'):
            filename = 'video.mp4'

    print(f"开始下载视频: {filename}")
    print(f"视频URL: {url}")

    try:
        # 发送请求
        response = requests.get(
            url,
            params=params,
            cookies=cookies,
            headers=headers,
            stream=True  # 使用流式下载，适合大文件
        )

        # 检查响应状态
        response.raise_for_status()

        # 获取文件大小
        total_size = int(response.headers.get('content-length', 0))

        # 下载文件
        downloaded_size = 0
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)

                    # 显示下载进度
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')
                    else:
                        print(f"\r已下载: {downloaded_size} bytes", end='')

        print(f"\n✅ 视频下载完成: {filename}")
        print(f"文件大小: {downloaded_size} bytes")
        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ 下载失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

# 视频URL
video_url = 'https://resources-sgp-auth.flextv.cc/wz/mp4/a/b/abd9961b60a8c28d3a4b19dec28a2335/b5d165f76cd08855282f1823ad44b405/1080p-max.mp4'

# 执行下载
if __name__ == "__main__":
    success = download_video(video_url, "downloaded_video.mp4")
    if success:
        print("🎉 下载任务完成！")
    else:
        print("💥 下载任务失败！")