#!/bin/bash

# 浏览器指纹学术研究项目启动器
# Browser Fingerprint Academic Research Project Launcher

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的文本
print_colored() {
    echo -e "${1}${2}${NC}"
}

# 打印标题
print_header() {
    echo
    echo "========================================"
    print_colored $CYAN "🎓 浏览器指纹学术研究项目启动器"
    print_colored $CYAN "Browser Fingerprint Academic Research"
    echo "========================================"
    echo
}

# 检查Python环境
check_python() {
    print_colored $BLUE "🔍 检查Python环境..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        print_colored $RED "❌ 未检测到Python，请先安装Python 3.6或更高版本"
        print_colored $YELLOW "📥 安装方法:"
        echo "   Ubuntu/Debian: sudo apt-get install python3"
        echo "   CentOS/RHEL:   sudo yum install python3"
        echo "   macOS:         brew install python3"
        exit 1
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    print_colored $GREEN "✅ Python环境检测成功 (版本: $PYTHON_VERSION)"
    echo
}

# 检查项目文件
check_files() {
    print_colored $BLUE "🔍 检查项目文件..."
    
    local missing_files=0
    local required_files=(
        "browser_fingerprint_collector.js"
        "fingerprint_analyzer.py"
        "启动项目.html"
        "学术研究工具使用指南.md"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            print_colored $RED "❌ 缺少文件: $file"
            missing_files=1
        fi
    done
    
    if [[ $missing_files -eq 1 ]]; then
        echo
        print_colored $RED "❌ 项目文件不完整，请确保所有文件都在当前目录中"
        exit 1
    fi
    
    print_colored $GREEN "✅ 项目文件检查完成"
    echo
}

# 显示项目信息
show_project_info() {
    print_colored $PURPLE "📋 项目信息:"
    echo "   📁 项目目录: $(pwd)"
    echo "   🔧 核心工具: browser_fingerprint_collector.js"
    echo "   📊 分析工具: fingerprint_analyzer.py"
    echo "   🚀 启动页面: 启动项目.html"
    echo "   📚 使用文档: 学术研究工具使用指南.md"
    echo
}

# 显示使用提醒
show_warning() {
    print_colored $YELLOW "⚠️ 重要提醒:"
    echo "   - 本工具仅用于学术研究和教育目的"
    echo "   - 请遵守相关法律法规和道德准则"
    echo "   - 不得用于恶意跟踪或隐私侵犯"
    echo
}

# 显示使用说明
show_usage() {
    print_colored $CYAN "🛠️ 使用说明:"
    echo "   1. 服务器启动后会自动打开浏览器"
    echo "   2. 在启动页面中点击'启动研究工具'"
    echo "   3. 或在目标网站中运行研究工具代码"
    echo "   4. 按 Ctrl+C 可以停止服务器"
    echo
    print_colored $BLUE "💡 提示: 请保持此终端窗口打开"
    echo
}

# 启动项目
start_project() {
    print_colored $GREEN "🚀 启动项目服务器..."
    echo
    
    # 给启动脚本执行权限
    chmod +x start_project.py 2>/dev/null
    
    # 启动Python服务器
    $PYTHON_CMD start_project.py
}

# 主函数
main() {
    # 清屏
    clear
    
    # 打印标题
    print_header
    
    # 检查Python环境
    check_python
    
    # 检查项目文件
    check_files
    
    # 显示项目信息
    show_project_info
    
    # 显示警告
    show_warning
    
    # 显示使用说明
    show_usage
    
    # 启动项目
    start_project
    
    # 退出信息
    echo
    print_colored $GREEN "👋 感谢使用浏览器指纹学术研究工具！"
}

# 捕获Ctrl+C信号
trap 'echo -e "\n🛑 正在停止服务器..."; exit 0' INT

# 运行主函数
main "$@"
