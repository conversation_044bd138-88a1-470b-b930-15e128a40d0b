#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlexTV 下载器安装脚本
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import json
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version}")
    return True

def install_pip_packages():
    """安装Python包"""
    print("📦 安装Python依赖包...")
    
    packages = [
        'selenium>=4.15.0',
        'requests>=2.31.0',
        'webdriver-manager>=4.0.0',
        'ffmpeg-python>=0.2.0',
        'beautifulsoup4>=4.12.0',
        'lxml>=4.9.0'
    ]
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    return True

def download_chromedriver():
    """下载ChromeDriver"""
    print("🌐 检查ChromeDriver...")
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        driver_path = ChromeDriverManager().install()
        print(f"✅ ChromeDriver已准备: {driver_path}")
        return True
    except Exception as e:
        print(f"❌ ChromeDriver安装失败: {e}")
        return False

def check_ffmpeg():
    """检查FFmpeg"""
    print("🎬 检查FFmpeg...")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg已安装")
            return True
    except FileNotFoundError:
        pass
    
    print("⚠️ 未找到FFmpeg")
    
    # 提供下载链接
    system = platform.system().lower()
    if system == 'windows':
        print("💡 Windows用户请访问: https://ffmpeg.org/download.html#build-windows")
        print("   或使用chocolatey: choco install ffmpeg")
    elif system == 'darwin':
        print("💡 macOS用户请使用Homebrew: brew install ffmpeg")
    elif system == 'linux':
        print("💡 Linux用户请使用包管理器:")
        print("   Ubuntu/Debian: sudo apt install ffmpeg")
        print("   CentOS/RHEL: sudo yum install ffmpeg")
    
    return False

def create_config_files():
    """创建配置文件"""
    print("📝 创建配置文件...")
    
    # 简化配置
    simple_config = {
        "video_url": "https://www.flextv.cc/video/The_Secret_Recipe_to_Snatch_a_Billionaire-J9zD5pbn1x",
        "download_dir": "downloads",
        "proxy": {
            "enabled": False,
            "host": "127.0.0.1",
            "port": "7890",
            "username": "",
            "password": ""
        },
        "browser": {
            "headless": False,
            "profile_path": "",
            "page_load_timeout": 30
        },
        "download": {
            "max_concurrent": 3,
            "retry_attempts": 3,
            "timeout": 300
        }
    }
    
    # 完整配置
    full_config = {
        "proxy": {
            "enabled": True,
            "host": "127.0.0.1",
            "port": "7890",
            "username": "",
            "password": "",
            "type": "http"
        },
        "fingerprint": {
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "window_size": "1920,1080",
            "language": "en-US",
            "timezone": "America/New_York"
        },
        "browser": {
            "profile_path": "",
            "headless": False,
            "disable_images": False,
            "page_load_timeout": 30,
            "implicit_wait": 10
        },
        "download": {
            "directory": "downloads",
            "max_concurrent": 3,
            "retry_attempts": 3,
            "chunk_size": 8192,
            "timeout": 300,
            "formats": ["mp4", "m3u8"],
            "quality_preference": ["1080p", "720p", "480p", "360p"]
        },
        "extraction": {
            "timeout": 300,
            "retry_attempts": 3,
            "delay_between_requests": 2,
            "max_concurrent_episodes": 3
        },
        "logging": {
            "level": "INFO",
            "file": "flextv_downloader.log",
            "format": "%(asctime)s - %(levelname)s - %(message)s"
        }
    }
    
    # 保存配置文件
    try:
        with open('simple_config.json', 'w', encoding='utf-8') as f:
            json.dump(simple_config, f, ensure_ascii=False, indent=2)
        print("✅ 简化配置文件已创建: simple_config.json")
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(full_config, f, ensure_ascii=False, indent=2)
        print("✅ 完整配置文件已创建: config.json")
        
        return True
    except Exception as e:
        print(f"❌ 配置文件创建失败: {e}")
        return False

def create_batch_files():
    """创建批处理文件"""
    print("📜 创建启动脚本...")
    
    # Windows批处理文件
    if platform.system().lower() == 'windows':
        batch_content = '''@echo off
echo FlexTV 下载器启动...
python run_downloader.py
pause
'''
        try:
            with open('start_downloader.bat', 'w', encoding='utf-8') as f:
                f.write(batch_content)
            print("✅ Windows启动脚本已创建: start_downloader.bat")
        except Exception as e:
            print(f"⚠️ 批处理文件创建失败: {e}")
    
    # Unix shell脚本
    shell_content = '''#!/bin/bash
echo "FlexTV 下载器启动..."
python3 run_downloader.py
'''
    try:
        with open('start_downloader.sh', 'w', encoding='utf-8') as f:
            f.write(shell_content)
        os.chmod('start_downloader.sh', 0o755)
        print("✅ Shell启动脚本已创建: start_downloader.sh")
    except Exception as e:
        print(f"⚠️ Shell脚本创建失败: {e}")

def create_readme():
    """创建使用说明"""
    readme_content = '''# FlexTV 自动下载器

## 🚀 快速开始

### 方法一：使用简化版本（推荐）
```bash
python run_downloader.py
```

### 方法二：使用完整版本
```bash
python flextv_auto_downloader.py
```

## ⚙️ 配置说明

### 简化配置 (simple_config.json)
- `video_url`: 要下载的视频页面URL
- `download_dir`: 下载目录
- `proxy`: 代理设置
- `browser`: 浏览器设置

### 完整配置 (config.json)
包含更多高级选项，如指纹浏览器配置、下载参数等。

## 🔧 代理设置

如果需要使用代理访问FlexTV：

```json
{
  "proxy": {
    "enabled": true,
    "host": "127.0.0.1",
    "port": "7890",
    "username": "用户名（可选）",
    "password": "密码（可选）"
  }
}
```

## 🎯 RoxyChrome配置

如果使用RoxyChrome等指纹浏览器：

```json
{
  "browser": {
    "profile_path": "/path/to/your/roxy/profile"
  }
}
```

## 📥 下载说明

1. 脚本会自动提取所有剧集的视频链接
2. 支持M3U8和MP4格式
3. 自动保存提取的数据为JSON文件
4. 可选择是否自动下载视频文件

## ⚠️ 注意事项

1. 需要稳定的网络连接
2. 建议使用代理访问FlexTV
3. 确保有足够的磁盘空间
4. 下载过程中请保持网络连接

## 🛠️ 故障排除

### 问题1：无法访问FlexTV
- 检查网络连接
- 确认代理设置正确
- 尝试使用指纹浏览器

### 问题2：视频下载失败
- 检查FFmpeg是否正确安装
- 确认视频链接有效性
- 尝试减少并发下载数量

### 问题3：提取超时
- 增加超时时间设置
- 检查页面是否正常加载
- 尝试刷新页面后重新运行
'''
    
    try:
        with open('README.md', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ 使用说明已创建: README.md")
    except Exception as e:
        print(f"⚠️ README创建失败: {e}")

def main():
    """主安装函数"""
    print("🎬 FlexTV 下载器安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装Python包
    if not install_pip_packages():
        print("❌ Python包安装失败")
        return False
    
    # 下载ChromeDriver
    if not download_chromedriver():
        print("⚠️ ChromeDriver安装失败，可能影响浏览器启动")
    
    # 检查FFmpeg
    has_ffmpeg = check_ffmpeg()
    if not has_ffmpeg:
        print("⚠️ FFmpeg未安装，将无法下载M3U8格式视频")
    
    # 创建配置文件
    if not create_config_files():
        return False
    
    # 创建启动脚本
    create_batch_files()
    
    # 创建使用说明
    create_readme()
    
    # 创建下载目录
    os.makedirs('downloads', exist_ok=True)
    print("✅ 下载目录已创建: downloads")
    
    print("\n🎉 安装完成！")
    print("\n📋 下一步:")
    print("1. 编辑配置文件设置代理和其他选项")
    print("2. 运行: python run_downloader.py")
    print("3. 或双击启动脚本文件")
    
    if not has_ffmpeg:
        print("\n⚠️ 建议安装FFmpeg以支持完整功能")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 安装失败！")
        sys.exit(1)
