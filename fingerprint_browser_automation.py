#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指纹浏览器API自动化脚本 - FlexTV视频提取
使用指纹浏览器API实现自动化操作
"""

import requests
import json
import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class FingerprintBrowserAutomation:
    def __init__(self, api_key, api_host="127.0.0.1", api_port="50000"):
        self.api_key = api_key
        self.api_host = api_host
        self.api_port = api_port
        self.base_url = f"http://{api_host}:{api_port}"
        self.driver = None
        self.profile_id = None
        self.extracted_data = {}
        
    def create_browser_profile(self, profile_name="FlexTV_Auto"):
        """创建浏览器配置文件"""
        url = f"{self.base_url}/api/v1/profile/create"
        
        profile_config = {
            "name": profile_name,
            "platform": "windows",
            "browserType": "chrome",
            "remark": "FlexTV自动提取配置",
            "userName": "",
            "password": "",
            "proxyMethod": 2,  # 自定义代理
            "proxyType": "http",
            "proxyHost": "127.0.0.1",
            "proxyPort": "7890",
            "proxyUser": "",
            "proxyPassword": "",
            "fingerprint": {
                "coreVersion": "112",
                "ostype": "PC",
                "os": "Windows",
                "osVersion": "10",
                "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "screenResolution": "1920x1080",
                "language": ["en-US", "en"],
                "timeZone": "America/New_York",
                "webRTC": "proxy",
                "canvas": "noise",
                "webGL": "noise",
                "webGLVendor": "Google Inc. (Intel)",
                "webGLRenderer": "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)",
                "audioContext": "noise",
                "fonts": "default",
                "hardwareConcurrency": 8,
                "deviceMemory": 8
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        try:
            response = requests.post(url, json=profile_config, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    self.profile_id = result["data"]["id"]
                    print(f"✅ 浏览器配置文件创建成功: {self.profile_id}")
                    return True
                else:
                    print(f"❌ 创建配置文件失败: {result.get('msg')}")
            else:
                print(f"❌ API请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 创建配置文件异常: {e}")
        
        return False
    
    def start_browser(self, profile_id=None):
        """启动指纹浏览器"""
        if profile_id:
            self.profile_id = profile_id
        
        if not self.profile_id:
            print("❌ 没有配置文件ID")
            return False
        
        url = f"{self.base_url}/api/v1/profile/start"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        data = {
            "id": self.profile_id
        }
        
        try:
            response = requests.post(url, json=data, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    debug_port = result["data"]["ws"]["selenium"]
                    print(f"✅ 浏览器启动成功，调试端口: {debug_port}")
                    
                    # 连接到浏览器
                    return self.connect_to_browser(debug_port)
                else:
                    print(f"❌ 启动浏览器失败: {result.get('msg')}")
            else:
                print(f"❌ API请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 启动浏览器异常: {e}")
        
        return False
    
    def connect_to_browser(self, debug_port):
        """连接到浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{debug_port}")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ 成功连接到指纹浏览器")
            return True
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            return False
    
    def stop_browser(self):
        """停止浏览器"""
        if self.driver:
            self.driver.quit()
            self.driver = None
        
        if self.profile_id:
            url = f"{self.base_url}/api/v1/profile/stop"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "id": self.profile_id
            }
            
            try:
                response = requests.post(url, json=data, headers=headers)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 0:
                        print("✅ 浏览器已停止")
                        return True
                    else:
                        print(f"⚠️ 停止浏览器失败: {result.get('msg')}")
                else:
                    print(f"⚠️ API请求失败: {response.status_code}")
            except Exception as e:
                print(f"⚠️ 停止浏览器异常: {e}")
        
        return False
    
    def inject_extractor_script(self):
        """注入视频提取脚本"""
        if not self.driver:
            print("❌ 浏览器未连接")
            return False
        
        # 尝试读取提取脚本
        script_files = [
            'flextv_advanced_extractor.js',
            'flextv_episodes_extractor.js',
            'flextv_video_extractor.js'
        ]
        
        for script_file in script_files:
            if os.path.exists(script_file):
                try:
                    with open(script_file, 'r', encoding='utf-8') as f:
                        script_content = f.read()
                    
                    self.driver.execute_script(script_content)
                    print(f"✅ 成功注入脚本: {script_file}")
                    return True
                except Exception as e:
                    print(f"⚠️ 注入脚本失败 {script_file}: {e}")
        
        print("❌ 没有找到可用的提取脚本")
        return False
    
    def wait_for_extraction(self, timeout=300):
        """等待提取完成"""
        if not self.driver:
            return False
        
        print("⏳ 等待视频信息提取完成...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查不同的全局变量
                for var_name in ['flextv_advanced_data', 'flextv_data']:
                    result = self.driver.execute_script(f"return window.{var_name};")
                    if result:
                        self.extracted_data = result
                        print("✅ 视频信息提取完成！")
                        
                        # 显示统计信息
                        total_episodes = result.get('totalEpisodes', 0)
                        successful = result.get('successfulExtractions', 0)
                        print(f"📊 统计: {successful}/{total_episodes} 集提取成功")
                        
                        return True
                
                time.sleep(3)
                
            except Exception as e:
                print(f"⚠️ 检查提取状态时出错: {e}")
                time.sleep(5)
        
        print("❌ 提取超时")
        return False
    
    def save_extracted_data(self):
        """保存提取的数据"""
        if not self.extracted_data:
            print("❌ 没有数据可保存")
            return False
        
        try:
            # 创建下载目录
            os.makedirs('downloads', exist_ok=True)
            
            # 保存完整数据
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            data_file = f"downloads/flextv_data_{timestamp}.json"
            
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 完整数据已保存: {data_file}")
            
            # 提取并保存视频链接
            video_links = []
            if 'videoData' in self.extracted_data:
                for episode in self.extracted_data['videoData']:
                    if episode.get('success') and episode.get('videoLinks'):
                        for link in episode['videoLinks']:
                            video_links.append({
                                'episode': episode['episodeNumber'],
                                'title': episode.get('episodeTitle', f"Episode {episode['episodeNumber']}"),
                                'url': link['url'],
                                'type': link['type'],
                                'quality': link.get('quality', 'unknown')
                            })
            elif 'allEpisodesData' in self.extracted_data:
                for episode in self.extracted_data['allEpisodesData']:
                    if episode.get('success') and episode.get('videoLinks'):
                        for link in episode['videoLinks']:
                            video_links.append({
                                'episode': episode['episodeNumber'],
                                'url': link['url'],
                                'type': link['type']
                            })
            
            if video_links:
                links_file = f"downloads/video_links_{timestamp}.json"
                with open(links_file, 'w', encoding='utf-8') as f:
                    json.dump(video_links, f, ensure_ascii=False, indent=2)
                
                print(f"🔗 视频链接已保存: {links_file}")
                print(f"🎥 总共找到 {len(video_links)} 个视频链接")
                
                # 显示前几个链接作为示例
                print("\n📺 视频链接示例:")
                for i, link in enumerate(video_links[:3]):
                    print(f"  第{link['episode']}集: {link['url'][:80]}...")
                
                if len(video_links) > 3:
                    print(f"  ... 还有 {len(video_links) - 3} 个链接")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
            return False
    
    def extract_flextv_video(self, video_url):
        """完整的FlexTV视频提取流程"""
        print("🚀 开始FlexTV视频自动提取...")
        print(f"🎬 目标视频: {video_url}")
        
        try:
            # 1. 创建浏览器配置文件
            if not self.create_browser_profile():
                return False
            
            # 2. 启动浏览器
            if not self.start_browser():
                return False
            
            # 3. 访问视频页面
            print(f"🌐 访问视频页面...")
            self.driver.get(video_url)
            time.sleep(5)
            
            # 4. 注入提取脚本
            if not self.inject_extractor_script():
                return False
            
            # 5. 等待提取完成
            if not self.wait_for_extraction():
                return False
            
            # 6. 保存数据
            if not self.save_extracted_data():
                return False
            
            print("🎉 视频信息提取完成！")
            return True
            
        except Exception as e:
            print(f"❌ 提取过程中出错: {e}")
            return False
        
        finally:
            # 清理资源
            self.stop_browser()

def main():
    """主函数"""
    print("🎬 指纹浏览器API自动化 - FlexTV视频提取")
    print("=" * 60)
    
    # 配置参数
    API_KEY = "6898fca8ed6927c4bbd74fab2f6f1357"
    API_HOST = "127.0.0.1"
    API_PORT = "50000"
    VIDEO_URL = "https://www.flextv.cc/video/The_Secret_Recipe_to_Snatch_a_Billionaire-J9zD5pbn1x"
    
    # 创建自动化实例
    automation = FingerprintBrowserAutomation(
        api_key=API_KEY,
        api_host=API_HOST,
        api_port=API_PORT
    )
    
    # 执行提取
    success = automation.extract_flextv_video(VIDEO_URL)
    
    if success:
        print("\n🎉 任务完成！")
        print("📁 请查看 downloads 目录中的提取结果")
    else:
        print("\n❌ 任务失败！")
        print("💡 请检查指纹浏览器API服务是否正常运行")

if __name__ == "__main__":
    main()
