// FlexTV 提取器测试脚本
// 用于测试不同提取器的功能和性能

(function() {
    'use strict';
    
    console.log('🧪 FlexTV 提取器测试工具');
    
    class ExtractorTester {
        constructor() {
            this.testResults = [];
            this.currentTest = null;
        }
        
        // 测试基础提取器
        async testBasicExtractor() {
            console.log('🔍 测试基础提取器...');
            this.currentTest = {
                name: 'Basic Extractor',
                startTime: Date.now(),
                success: false,
                data: null,
                error: null
            };
            
            try {
                // 模拟基础提取器的核心功能
                const basicData = {
                    title: document.title.replace(' - FlexTV', ''),
                    seriesId: this.extractSeriesId(),
                    pageUrl: window.location.href,
                    extractTime: new Date().toISOString()
                };
                
                this.currentTest.success = true;
                this.currentTest.data = basicData;
                this.currentTest.duration = Date.now() - this.currentTest.startTime;
                
                console.log('✅ 基础提取器测试通过');
                
            } catch (error) {
                this.currentTest.error = error.message;
                console.error('❌ 基础提取器测试失败:', error);
            }
            
            this.testResults.push(this.currentTest);
        }
        
        // 测试API连接
        async testApiConnection() {
            console.log('🌐 测试API连接...');
            this.currentTest = {
                name: 'API Connection',
                startTime: Date.now(),
                success: false,
                data: null,
                error: null
            };
            
            const seriesId = this.extractSeriesId();
            if (!seriesId) {
                this.currentTest.error = 'No series ID found';
                this.testResults.push(this.currentTest);
                return;
            }
            
            try {
                const apiUrl = `https://api-quick.flextv.cc/webGetSeriesSectionFullList?series_id=${seriesId}&series_no=1`;
                console.log(`📡 测试API: ${apiUrl}`);
                
                const response = await fetch(apiUrl);
                const data = await response.json();
                
                this.currentTest.success = response.ok;
                this.currentTest.data = {
                    status: response.status,
                    dataReceived: !!data,
                    episodeCount: data && data.data ? data.data.length : 0
                };
                this.currentTest.duration = Date.now() - this.currentTest.startTime;
                
                if (response.ok) {
                    console.log(`✅ API连接成功，找到 ${this.currentTest.data.episodeCount} 集`);
                } else {
                    console.log(`⚠️ API响应异常: ${response.status}`);
                }
                
            } catch (error) {
                this.currentTest.error = error.message;
                console.error('❌ API连接测试失败:', error);
            }
            
            this.testResults.push(this.currentTest);
        }
        
        // 测试页面元素提取
        async testPageElementExtraction() {
            console.log('🔍 测试页面元素提取...');
            this.currentTest = {
                name: 'Page Element Extraction',
                startTime: Date.now(),
                success: false,
                data: null,
                error: null
            };
            
            try {
                const elements = {
                    videoElements: document.querySelectorAll('video').length,
                    sourceElements: document.querySelectorAll('video source').length,
                    episodeButtons: document.querySelectorAll('[class*="episode"], [class*="section"]').length,
                    scriptTags: document.querySelectorAll('script').length
                };
                
                // 查找可能的视频URL
                const scripts = Array.from(document.querySelectorAll('script'));
                let videoUrlsFound = 0;
                
                scripts.forEach(script => {
                    const content = script.textContent || script.innerHTML;
                    const m3u8Matches = content.match(/https?:\/\/[^\s"']+\.m3u8[^\s"']*/g);
                    const mp4Matches = content.match(/https?:\/\/[^\s"']+\.mp4[^\s"']*/g);
                    
                    if (m3u8Matches) videoUrlsFound += m3u8Matches.length;
                    if (mp4Matches) videoUrlsFound += mp4Matches.length;
                });
                
                elements.videoUrlsInScripts = videoUrlsFound;
                
                this.currentTest.success = true;
                this.currentTest.data = elements;
                this.currentTest.duration = Date.now() - this.currentTest.startTime;
                
                console.log('✅ 页面元素提取完成:', elements);
                
            } catch (error) {
                this.currentTest.error = error.message;
                console.error('❌ 页面元素提取失败:', error);
            }
            
            this.testResults.push(this.currentTest);
        }
        
        // 测试网络请求拦截
        async testNetworkInterception() {
            console.log('🕸️ 测试网络请求拦截...');
            this.currentTest = {
                name: 'Network Interception',
                startTime: Date.now(),
                success: false,
                data: null,
                error: null
            };
            
            try {
                const interceptedRequests = [];
                const originalFetch = window.fetch;
                
                // 临时拦截fetch请求
                window.fetch = function(...args) {
                    const url = args[0];
                    if (typeof url === 'string' && (
                        url.includes('api-quick.flextv.cc') || 
                        url.includes('video') || 
                        url.includes('m3u8') || 
                        url.includes('mp4')
                    )) {
                        interceptedRequests.push({
                            url: url,
                            timestamp: Date.now()
                        });
                    }
                    return originalFetch.apply(this, args);
                };
                
                // 等待一段时间收集请求
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 恢复原始fetch
                window.fetch = originalFetch;
                
                this.currentTest.success = true;
                this.currentTest.data = {
                    interceptedCount: interceptedRequests.length,
                    requests: interceptedRequests
                };
                this.currentTest.duration = Date.now() - this.currentTest.startTime;
                
                console.log(`✅ 网络拦截测试完成，拦截到 ${interceptedRequests.length} 个相关请求`);
                
            } catch (error) {
                this.currentTest.error = error.message;
                console.error('❌ 网络拦截测试失败:', error);
            }
            
            this.testResults.push(this.currentTest);
        }
        
        // 从URL提取series_id
        extractSeriesId() {
            const urlMatch = window.location.pathname.match(/([^-]+)$/);
            return urlMatch ? urlMatch[1] : null;
        }
        
        // 运行所有测试
        async runAllTests() {
            console.log('🚀 开始运行所有测试...');
            
            await this.testBasicExtractor();
            await this.testApiConnection();
            await this.testPageElementExtraction();
            await this.testNetworkInterception();
            
            this.generateReport();
        }
        
        // 生成测试报告
        generateReport() {
            console.log('\n📊 测试报告');
            console.log('='.repeat(50));
            
            const totalTests = this.testResults.length;
            const passedTests = this.testResults.filter(test => test.success).length;
            const failedTests = totalTests - passedTests;
            
            console.log(`总测试数: ${totalTests}`);
            console.log(`通过: ${passedTests} ✅`);
            console.log(`失败: ${failedTests} ❌`);
            console.log(`成功率: ${Math.round((passedTests / totalTests) * 100)}%`);
            
            console.log('\n详细结果:');
            this.testResults.forEach((test, index) => {
                const status = test.success ? '✅' : '❌';
                const duration = test.duration ? `(${test.duration}ms)` : '';
                console.log(`${index + 1}. ${status} ${test.name} ${duration}`);
                
                if (test.error) {
                    console.log(`   错误: ${test.error}`);
                }
                
                if (test.data && typeof test.data === 'object') {
                    console.log(`   数据:`, test.data);
                }
            });
            
            // 生成建议
            this.generateRecommendations();
            
            // 保存测试结果到全局变量
            window.flextv_test_results = {
                summary: {
                    total: totalTests,
                    passed: passedTests,
                    failed: failedTests,
                    successRate: Math.round((passedTests / totalTests) * 100)
                },
                details: this.testResults,
                timestamp: new Date().toISOString()
            };
            
            console.log('\n💾 测试结果已保存到 window.flextv_test_results');
        }
        
        // 生成使用建议
        generateRecommendations() {
            console.log('\n💡 使用建议:');
            
            const apiTest = this.testResults.find(test => test.name === 'API Connection');
            const elementTest = this.testResults.find(test => test.name === 'Page Element Extraction');
            
            if (apiTest && apiTest.success && apiTest.data.episodeCount > 0) {
                console.log('🚀 推荐使用: flextv_advanced_extractor.js (API优化版)');
                console.log('   原因: API连接正常，可以高效获取所有剧集信息');
            } else if (elementTest && elementTest.success && elementTest.data.episodeButtons > 0) {
                console.log('🔄 推荐使用: flextv_episodes_extractor.js (iframe版)');
                console.log('   原因: 页面有剧集按钮，可以通过iframe方式提取');
            } else {
                console.log('⚠️ 推荐使用: flextv_video_extractor.js (基础版)');
                console.log('   原因: 当前环境限制较多，建议使用基础提取器');
            }
            
            if (elementTest && elementTest.data && elementTest.data.videoUrlsInScripts > 0) {
                console.log(`📺 页面脚本中发现 ${elementTest.data.videoUrlsInScripts} 个视频URL`);
            }
        }
    }
    
    // 创建测试实例并运行
    const tester = new ExtractorTester();
    tester.runAllTests().then(() => {
        console.log('\n🎉 所有测试完成！');
    }).catch(error => {
        console.error('❌ 测试过程中出现错误:', error);
    });
    
})();

console.log(`
🧪 FlexTV 提取器测试工具

功能：
✅ 测试基础提取功能
✅ 测试API连接状态
✅ 测试页面元素提取
✅ 测试网络请求拦截
✅ 生成详细测试报告
✅ 提供使用建议

使用方法：
1. 在FlexTV视频页面打开开发者工具
2. 粘贴此脚本到控制台并执行
3. 等待测试完成，查看报告和建议

测试结果将保存到 window.flextv_test_results
`);
