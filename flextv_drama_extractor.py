#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlexTV短剧数据提取器
从FlexTV网站提取短剧信息，包括图片、标题、简介和视频链接
"""

import json
import time
import requests
from bs4 import BeautifulSoup
import csv
from urllib.parse import urljoin

class FlexTVExtractor:
    def __init__(self):
        self.base_url = "https://www.flextv.cc/"
        self.dramas = []
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def fetch_page(self, url):
        """获取网页内容"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            print(f"获取页面失败: {e}")
            return None
    
    def extract_drama_data(self, html_content):
        """从HTML内容中提取短剧数据"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 查找所有短剧项目
            drama_items = soup.select('.drama_list .item')
            print(f"找到 {len(drama_items)} 个短剧项目")

            for index, item in enumerate(drama_items):
                try:
                    # 提取基本信息
                    link_element = item.select_one('a')
                    img_element = item.select_one('.img')
                    title_element = item.select_one('.title')

                    if not all([link_element, img_element, title_element]):
                        continue

                    # 提取描述信息
                    description = ""
                    desc_element = item.select_one('.hover .desc')
                    if desc_element:
                        description = desc_element.get_text(strip=True)

                    # 提取类型信息
                    category = ""
                    type_meta = item.select_one('meta[itemprop="type"]')
                    if type_meta:
                        category = type_meta.get('content', '')

                    # 提取简介信息
                    intro = ""
                    intro_meta = item.select_one('meta[itemprop="introduce"]')
                    if intro_meta:
                        intro = intro_meta.get('content', '')

                    drama = {
                        'id': len(self.dramas) + 1,
                        'title': title_element.get_text(strip=True),
                        'image': img_element.get('src', ''),
                        'description': description or intro,
                        'videoLink': urljoin(self.base_url, link_element.get('href', '')),
                        'category': category
                    }

                    self.dramas.append(drama)
                    print(f"提取第 {len(self.dramas)} 个短剧: {drama['title']}")

                except Exception as e:
                    print(f"提取第 {index + 1} 个短剧时出错: {e}")
                    continue

        except Exception as e:
            print(f"解析HTML时出错: {e}")
    
    def save_to_json(self, filename="flextv_dramas.json"):
        """保存数据到JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.dramas, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到 {filename}")

    def save_to_csv(self, filename="flextv_dramas.csv"):
        """保存数据到CSV文件"""
        if not self.dramas:
            print("没有数据可保存")
            return

        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['id', 'title', 'category', 'description', 'image', 'videoLink'])
            writer.writeheader()
            writer.writerows(self.dramas)
        print(f"数据已保存到 {filename}")

    def run(self):
        """运行提取器"""
        try:
            print(f"访问 {self.base_url}")
            html_content = self.fetch_page(self.base_url)

            if not html_content:
                print("无法获取页面内容")
                return

            print("开始提取短剧数据...")
            self.extract_drama_data(html_content)

            print(f"提取完成，共获取 {len(self.dramas)} 个短剧")

            if self.dramas:
                # 保存数据
                self.save_to_json()
                self.save_to_csv()

                # 显示前几个示例
                print("\n前5个短剧示例:")
                for i, drama in enumerate(self.dramas[:5]):
                    print(f"{i+1}. {drama['title']}")
                    print(f"   类型: {drama['category']}")
                    print(f"   链接: {drama['videoLink']}")
                    print(f"   图片: {drama['image']}")
                    print(f"   简介: {drama['description'][:100]}...")
                    print()
            else:
                print("未提取到任何数据")

        except Exception as e:
            print(f"运行时出错: {e}")

if __name__ == "__main__":
    extractor = FlexTVExtractor()
    extractor.run()
